# DO NOT EDIT THIS FILE!
#
# This file is generated from the CDP specification. If you need to make
# changes, edit the generator and regenerate all of the modules.
#
# CDP domain: Debugger
from __future__ import annotations
from .util import event_class, T_JSON_DICT
from dataclasses import dataclass
import enum
import typing
from . import runtime


class BreakpointId(str):
    '''
    Breakpoint identifier.
    '''
    def to_json(self) -> str:
        return self

    @classmethod
    def from_json(cls, json: str) -> BreakpointId:
        return cls(json)

    def __repr__(self):
        return 'BreakpointId({})'.format(super().__repr__())


class CallFrameId(str):
    '''
    Call frame identifier.
    '''
    def to_json(self) -> str:
        return self

    @classmethod
    def from_json(cls, json: str) -> CallFrameId:
        return cls(json)

    def __repr__(self):
        return 'CallFrameId({})'.format(super().__repr__())


@dataclass
class Location:
    '''
    Location in the source code.
    '''
    #: Script identifier as reported in the ``Debugger.scriptParsed``.
    script_id: runtime.ScriptId

    #: Line number in the script (0-based).
    line_number: int

    #: Column number in the script (0-based).
    column_number: typing.Optional[int] = None

    def to_json(self):
        json = dict()
        json['scriptId'] = self.script_id.to_json()
        json['lineNumber'] = self.line_number
        if self.column_number is not None:
            json['columnNumber'] = self.column_number
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            script_id=runtime.ScriptId.from_json(json['scriptId']),
            line_number=int(json['lineNumber']),
            column_number=int(json['columnNumber']) if 'columnNumber' in json else None,
        )


@dataclass
class ScriptPosition:
    '''
    Location in the source code.
    '''
    line_number: int

    column_number: int

    def to_json(self):
        json = dict()
        json['lineNumber'] = self.line_number
        json['columnNumber'] = self.column_number
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            line_number=int(json['lineNumber']),
            column_number=int(json['columnNumber']),
        )


@dataclass
class LocationRange:
    '''
    Location range within one script.
    '''
    script_id: runtime.ScriptId

    start: ScriptPosition

    end: ScriptPosition

    def to_json(self):
        json = dict()
        json['scriptId'] = self.script_id.to_json()
        json['start'] = self.start.to_json()
        json['end'] = self.end.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            script_id=runtime.ScriptId.from_json(json['scriptId']),
            start=ScriptPosition.from_json(json['start']),
            end=ScriptPosition.from_json(json['end']),
        )


@dataclass
class CallFrame:
    '''
    JavaScript call frame. Array of call frames form the call stack.
    '''
    #: Call frame identifier. This identifier is only valid while the virtual machine is paused.
    call_frame_id: CallFrameId

    #: Name of the JavaScript function called on this call frame.
    function_name: str

    #: Location in the source code.
    location: Location

    #: JavaScript script name or url.
    #: Deprecated in favor of using the ``location.scriptId`` to resolve the URL via a previously
    #: sent ``Debugger.scriptParsed`` event.
    url: str

    #: Scope chain for this call frame.
    scope_chain: typing.List[Scope]

    #: ``this`` object for this call frame.
    this: runtime.RemoteObject

    #: Location in the source code.
    function_location: typing.Optional[Location] = None

    #: The value being returned, if the function is at return point.
    return_value: typing.Optional[runtime.RemoteObject] = None

    #: Valid only while the VM is paused and indicates whether this frame
    #: can be restarted or not. Note that a ``true`` value here does not
    #: guarantee that Debugger#restartFrame with this CallFrameId will be
    #: successful, but it is very likely.
    can_be_restarted: typing.Optional[bool] = None

    def to_json(self):
        json = dict()
        json['callFrameId'] = self.call_frame_id.to_json()
        json['functionName'] = self.function_name
        json['location'] = self.location.to_json()
        json['url'] = self.url
        json['scopeChain'] = [i.to_json() for i in self.scope_chain]
        json['this'] = self.this.to_json()
        if self.function_location is not None:
            json['functionLocation'] = self.function_location.to_json()
        if self.return_value is not None:
            json['returnValue'] = self.return_value.to_json()
        if self.can_be_restarted is not None:
            json['canBeRestarted'] = self.can_be_restarted
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            call_frame_id=CallFrameId.from_json(json['callFrameId']),
            function_name=str(json['functionName']),
            location=Location.from_json(json['location']),
            url=str(json['url']),
            scope_chain=[Scope.from_json(i) for i in json['scopeChain']],
            this=runtime.RemoteObject.from_json(json['this']),
            function_location=Location.from_json(json['functionLocation']) if 'functionLocation' in json else None,
            return_value=runtime.RemoteObject.from_json(json['returnValue']) if 'returnValue' in json else None,
            can_be_restarted=bool(json['canBeRestarted']) if 'canBeRestarted' in json else None,
        )


@dataclass
class Scope:
    '''
    Scope description.
    '''
    #: Scope type.
    type_: str

    #: Object representing the scope. For ``global`` and ``with`` scopes it represents the actual
    #: object; for the rest of the scopes, it is artificial transient object enumerating scope
    #: variables as its properties.
    object_: runtime.RemoteObject

    name: typing.Optional[str] = None

    #: Location in the source code where scope starts
    start_location: typing.Optional[Location] = None

    #: Location in the source code where scope ends
    end_location: typing.Optional[Location] = None

    def to_json(self):
        json = dict()
        json['type'] = self.type_
        json['object'] = self.object_.to_json()
        if self.name is not None:
            json['name'] = self.name
        if self.start_location is not None:
            json['startLocation'] = self.start_location.to_json()
        if self.end_location is not None:
            json['endLocation'] = self.end_location.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            type_=str(json['type']),
            object_=runtime.RemoteObject.from_json(json['object']),
            name=str(json['name']) if 'name' in json else None,
            start_location=Location.from_json(json['startLocation']) if 'startLocation' in json else None,
            end_location=Location.from_json(json['endLocation']) if 'endLocation' in json else None,
        )


@dataclass
class SearchMatch:
    '''
    Search match for resource.
    '''
    #: Line number in resource content.
    line_number: float

    #: Line with match content.
    line_content: str

    def to_json(self):
        json = dict()
        json['lineNumber'] = self.line_number
        json['lineContent'] = self.line_content
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            line_number=float(json['lineNumber']),
            line_content=str(json['lineContent']),
        )


@dataclass
class BreakLocation:
    #: Script identifier as reported in the ``Debugger.scriptParsed``.
    script_id: runtime.ScriptId

    #: Line number in the script (0-based).
    line_number: int

    #: Column number in the script (0-based).
    column_number: typing.Optional[int] = None

    type_: typing.Optional[str] = None

    def to_json(self):
        json = dict()
        json['scriptId'] = self.script_id.to_json()
        json['lineNumber'] = self.line_number
        if self.column_number is not None:
            json['columnNumber'] = self.column_number
        if self.type_ is not None:
            json['type'] = self.type_
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            script_id=runtime.ScriptId.from_json(json['scriptId']),
            line_number=int(json['lineNumber']),
            column_number=int(json['columnNumber']) if 'columnNumber' in json else None,
            type_=str(json['type']) if 'type' in json else None,
        )


@dataclass
class WasmDisassemblyChunk:
    #: The next chunk of disassembled lines.
    lines: typing.List[str]

    #: The bytecode offsets describing the start of each line.
    bytecode_offsets: typing.List[int]

    def to_json(self):
        json = dict()
        json['lines'] = [i for i in self.lines]
        json['bytecodeOffsets'] = [i for i in self.bytecode_offsets]
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            lines=[str(i) for i in json['lines']],
            bytecode_offsets=[int(i) for i in json['bytecodeOffsets']],
        )


class ScriptLanguage(enum.Enum):
    '''
    Enum of possible script languages.
    '''
    JAVA_SCRIPT = "JavaScript"
    WEB_ASSEMBLY = "WebAssembly"

    def to_json(self):
        return self.value

    @classmethod
    def from_json(cls, json):
        return cls(json)


@dataclass
class DebugSymbols:
    '''
    Debug symbols available for a wasm script.
    '''
    #: Type of the debug symbols.
    type_: str

    #: URL of the external symbol source.
    external_url: typing.Optional[str] = None

    def to_json(self):
        json = dict()
        json['type'] = self.type_
        if self.external_url is not None:
            json['externalURL'] = self.external_url
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            type_=str(json['type']),
            external_url=str(json['externalURL']) if 'externalURL' in json else None,
        )


@dataclass
class ResolvedBreakpoint:
    #: Breakpoint unique identifier.
    breakpoint_id: BreakpointId

    #: Actual breakpoint location.
    location: Location

    def to_json(self):
        json = dict()
        json['breakpointId'] = self.breakpoint_id.to_json()
        json['location'] = self.location.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            breakpoint_id=BreakpointId.from_json(json['breakpointId']),
            location=Location.from_json(json['location']),
        )


def continue_to_location(
        location: Location,
        target_call_frames: typing.Optional[str] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Continues execution until specific location is reached.

    :param location: Location to continue to.
    :param target_call_frames: *(Optional)*
    '''
    params: T_JSON_DICT = dict()
    params['location'] = location.to_json()
    if target_call_frames is not None:
        params['targetCallFrames'] = target_call_frames
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.continueToLocation',
        'params': params,
    }
    json = yield cmd_dict


def disable() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Disables debugger for given page.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.disable',
    }
    json = yield cmd_dict


def enable(
        max_scripts_cache_size: typing.Optional[float] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,runtime.UniqueDebuggerId]:
    '''
    Enables debugger for the given page. Clients should not assume that the debugging has been
    enabled until the result for this command is received.

    :param max_scripts_cache_size: **(EXPERIMENTAL)** *(Optional)* The maximum size in bytes of collected scripts (not referenced by other heap objects) the debugger can hold. Puts no limit if parameter is omitted.
    :returns: Unique identifier of the debugger.
    '''
    params: T_JSON_DICT = dict()
    if max_scripts_cache_size is not None:
        params['maxScriptsCacheSize'] = max_scripts_cache_size
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.enable',
        'params': params,
    }
    json = yield cmd_dict
    return runtime.UniqueDebuggerId.from_json(json['debuggerId'])


def evaluate_on_call_frame(
        call_frame_id: CallFrameId,
        expression: str,
        object_group: typing.Optional[str] = None,
        include_command_line_api: typing.Optional[bool] = None,
        silent: typing.Optional[bool] = None,
        return_by_value: typing.Optional[bool] = None,
        generate_preview: typing.Optional[bool] = None,
        throw_on_side_effect: typing.Optional[bool] = None,
        timeout: typing.Optional[runtime.TimeDelta] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[runtime.RemoteObject, typing.Optional[runtime.ExceptionDetails]]]:
    '''
    Evaluates expression on a given call frame.

    :param call_frame_id: Call frame identifier to evaluate on.
    :param expression: Expression to evaluate.
    :param object_group: *(Optional)* String object group name to put result into (allows rapid releasing resulting object handles using ```releaseObjectGroup````).
    :param include_command_line_api: *(Optional)* Specifies whether command line API should be available to the evaluated expression, defaults to false.
    :param silent: *(Optional)* In silent mode exceptions thrown during evaluation are not reported and do not pause execution. Overrides ````setPauseOnException``` state.
    :param return_by_value: *(Optional)* Whether the result is expected to be a JSON object that should be sent by value.
    :param generate_preview: **(EXPERIMENTAL)** *(Optional)* Whether preview should be generated for the result.
    :param throw_on_side_effect: *(Optional)* Whether to throw an exception if side effect cannot be ruled out during evaluation.
    :param timeout: **(EXPERIMENTAL)** *(Optional)* Terminate execution after timing out (number of milliseconds).
    :returns: A tuple with the following items:

        0. **result** - Object wrapper for the evaluation result.
        1. **exceptionDetails** - *(Optional)* Exception details.
    '''
    params: T_JSON_DICT = dict()
    params['callFrameId'] = call_frame_id.to_json()
    params['expression'] = expression
    if object_group is not None:
        params['objectGroup'] = object_group
    if include_command_line_api is not None:
        params['includeCommandLineAPI'] = include_command_line_api
    if silent is not None:
        params['silent'] = silent
    if return_by_value is not None:
        params['returnByValue'] = return_by_value
    if generate_preview is not None:
        params['generatePreview'] = generate_preview
    if throw_on_side_effect is not None:
        params['throwOnSideEffect'] = throw_on_side_effect
    if timeout is not None:
        params['timeout'] = timeout.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.evaluateOnCallFrame',
        'params': params,
    }
    json = yield cmd_dict
    return (
        runtime.RemoteObject.from_json(json['result']),
        runtime.ExceptionDetails.from_json(json['exceptionDetails']) if 'exceptionDetails' in json else None
    )


def get_possible_breakpoints(
        start: Location,
        end: typing.Optional[Location] = None,
        restrict_to_function: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[BreakLocation]]:
    '''
    Returns possible locations for breakpoint. scriptId in start and end range locations should be
    the same.

    :param start: Start of range to search possible breakpoint locations in.
    :param end: *(Optional)* End of range to search possible breakpoint locations in (excluding). When not specified, end of scripts is used as end of range.
    :param restrict_to_function: *(Optional)* Only consider locations which are in the same (non-nested) function as start.
    :returns: List of the possible breakpoint locations.
    '''
    params: T_JSON_DICT = dict()
    params['start'] = start.to_json()
    if end is not None:
        params['end'] = end.to_json()
    if restrict_to_function is not None:
        params['restrictToFunction'] = restrict_to_function
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.getPossibleBreakpoints',
        'params': params,
    }
    json = yield cmd_dict
    return [BreakLocation.from_json(i) for i in json['locations']]


def get_script_source(
        script_id: runtime.ScriptId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[str, typing.Optional[str]]]:
    '''
    Returns source for the script with given id.

    :param script_id: Id of the script to get source for.
    :returns: A tuple with the following items:

        0. **scriptSource** - Script source (empty in case of Wasm bytecode).
        1. **bytecode** - *(Optional)* Wasm bytecode.
    '''
    params: T_JSON_DICT = dict()
    params['scriptId'] = script_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.getScriptSource',
        'params': params,
    }
    json = yield cmd_dict
    return (
        str(json['scriptSource']),
        str(json['bytecode']) if 'bytecode' in json else None
    )


def disassemble_wasm_module(
        script_id: runtime.ScriptId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[typing.Optional[str], int, typing.List[int], WasmDisassemblyChunk]]:
    '''


    **EXPERIMENTAL**

    :param script_id: Id of the script to disassemble
    :returns: A tuple with the following items:

        0. **streamId** - *(Optional)* For large modules, return a stream from which additional chunks of disassembly can be read successively.
        1. **totalNumberOfLines** - The total number of lines in the disassembly text.
        2. **functionBodyOffsets** - The offsets of all function bodies, in the format [start1, end1, start2, end2, ...] where all ends are exclusive.
        3. **chunk** - The first chunk of disassembly.
    '''
    params: T_JSON_DICT = dict()
    params['scriptId'] = script_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.disassembleWasmModule',
        'params': params,
    }
    json = yield cmd_dict
    return (
        str(json['streamId']) if 'streamId' in json else None,
        int(json['totalNumberOfLines']),
        [int(i) for i in json['functionBodyOffsets']],
        WasmDisassemblyChunk.from_json(json['chunk'])
    )


def next_wasm_disassembly_chunk(
        stream_id: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,WasmDisassemblyChunk]:
    '''
    Disassemble the next chunk of lines for the module corresponding to the
    stream. If disassembly is complete, this API will invalidate the streamId
    and return an empty chunk. Any subsequent calls for the now invalid stream
    will return errors.

    **EXPERIMENTAL**

    :param stream_id:
    :returns: The next chunk of disassembly.
    '''
    params: T_JSON_DICT = dict()
    params['streamId'] = stream_id
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.nextWasmDisassemblyChunk',
        'params': params,
    }
    json = yield cmd_dict
    return WasmDisassemblyChunk.from_json(json['chunk'])


def get_wasm_bytecode(
        script_id: runtime.ScriptId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,str]:
    '''
    This command is deprecated. Use getScriptSource instead.

    :param script_id: Id of the Wasm script to get source for.
    :returns: Script source.
    '''
    params: T_JSON_DICT = dict()
    params['scriptId'] = script_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.getWasmBytecode',
        'params': params,
    }
    json = yield cmd_dict
    return str(json['bytecode'])


def get_stack_trace(
        stack_trace_id: runtime.StackTraceId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,runtime.StackTrace]:
    '''
    Returns stack trace with given ``stackTraceId``.

    **EXPERIMENTAL**

    :param stack_trace_id:
    :returns: 
    '''
    params: T_JSON_DICT = dict()
    params['stackTraceId'] = stack_trace_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.getStackTrace',
        'params': params,
    }
    json = yield cmd_dict
    return runtime.StackTrace.from_json(json['stackTrace'])


def pause() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Stops on the next JavaScript statement.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.pause',
    }
    json = yield cmd_dict


def pause_on_async_call(
        parent_stack_trace_id: runtime.StackTraceId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''


    **EXPERIMENTAL**

    :param parent_stack_trace_id: Debugger will pause when async call with given stack trace is started.
    '''
    params: T_JSON_DICT = dict()
    params['parentStackTraceId'] = parent_stack_trace_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.pauseOnAsyncCall',
        'params': params,
    }
    json = yield cmd_dict


def remove_breakpoint(
        breakpoint_id: BreakpointId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Removes JavaScript breakpoint.

    :param breakpoint_id:
    '''
    params: T_JSON_DICT = dict()
    params['breakpointId'] = breakpoint_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.removeBreakpoint',
        'params': params,
    }
    json = yield cmd_dict


def restart_frame(
        call_frame_id: CallFrameId,
        mode: typing.Optional[str] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[typing.List[CallFrame], typing.Optional[runtime.StackTrace], typing.Optional[runtime.StackTraceId]]]:
    '''
    Restarts particular call frame from the beginning. The old, deprecated
    behavior of ``restartFrame`` is to stay paused and allow further CDP commands
    after a restart was scheduled. This can cause problems with restarting, so
    we now continue execution immediatly after it has been scheduled until we
    reach the beginning of the restarted frame.

    To stay back-wards compatible, ``restartFrame`` now expects a ``mode``
    parameter to be present. If the ``mode`` parameter is missing, ``restartFrame``
    errors out.

    The various return values are deprecated and ``callFrames`` is always empty.
    Use the call frames from the ``Debugger#paused`` events instead, that fires
    once V8 pauses at the beginning of the restarted function.

    :param call_frame_id: Call frame identifier to evaluate on.
    :param mode: **(EXPERIMENTAL)** *(Optional)* The ```mode```` parameter must be present and set to 'StepInto', otherwise ````restartFrame``` will error out.
    :returns: A tuple with the following items:

        0. **callFrames** - New stack trace.
        1. **asyncStackTrace** - *(Optional)* Async stack trace, if any.
        2. **asyncStackTraceId** - *(Optional)* Async stack trace, if any.
    '''
    params: T_JSON_DICT = dict()
    params['callFrameId'] = call_frame_id.to_json()
    if mode is not None:
        params['mode'] = mode
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.restartFrame',
        'params': params,
    }
    json = yield cmd_dict
    return (
        [CallFrame.from_json(i) for i in json['callFrames']],
        runtime.StackTrace.from_json(json['asyncStackTrace']) if 'asyncStackTrace' in json else None,
        runtime.StackTraceId.from_json(json['asyncStackTraceId']) if 'asyncStackTraceId' in json else None
    )


def resume(
        terminate_on_resume: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Resumes JavaScript execution.

    :param terminate_on_resume: *(Optional)* Set to true to terminate execution upon resuming execution. In contrast to Runtime.terminateExecution, this will allows to execute further JavaScript (i.e. via evaluation) until execution of the paused code is actually resumed, at which point termination is triggered. If execution is currently not paused, this parameter has no effect.
    '''
    params: T_JSON_DICT = dict()
    if terminate_on_resume is not None:
        params['terminateOnResume'] = terminate_on_resume
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.resume',
        'params': params,
    }
    json = yield cmd_dict


def search_in_content(
        script_id: runtime.ScriptId,
        query: str,
        case_sensitive: typing.Optional[bool] = None,
        is_regex: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[SearchMatch]]:
    '''
    Searches for given string in script content.

    :param script_id: Id of the script to search in.
    :param query: String to search for.
    :param case_sensitive: *(Optional)* If true, search is case sensitive.
    :param is_regex: *(Optional)* If true, treats string parameter as regex.
    :returns: List of search matches.
    '''
    params: T_JSON_DICT = dict()
    params['scriptId'] = script_id.to_json()
    params['query'] = query
    if case_sensitive is not None:
        params['caseSensitive'] = case_sensitive
    if is_regex is not None:
        params['isRegex'] = is_regex
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.searchInContent',
        'params': params,
    }
    json = yield cmd_dict
    return [SearchMatch.from_json(i) for i in json['result']]


def set_async_call_stack_depth(
        max_depth: int
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Enables or disables async call stacks tracking.

    :param max_depth: Maximum depth of async call stacks. Setting to ```0``` will effectively disable collecting async call stacks (default).
    '''
    params: T_JSON_DICT = dict()
    params['maxDepth'] = max_depth
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setAsyncCallStackDepth',
        'params': params,
    }
    json = yield cmd_dict


def set_blackbox_execution_contexts(
        unique_ids: typing.List[str]
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Replace previous blackbox execution contexts with passed ones. Forces backend to skip
    stepping/pausing in scripts in these execution contexts. VM will try to leave blackboxed script by
    performing 'step in' several times, finally resorting to 'step out' if unsuccessful.

    **EXPERIMENTAL**

    :param unique_ids: Array of execution context unique ids for the debugger to ignore.
    '''
    params: T_JSON_DICT = dict()
    params['uniqueIds'] = [i for i in unique_ids]
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setBlackboxExecutionContexts',
        'params': params,
    }
    json = yield cmd_dict


def set_blackbox_patterns(
        patterns: typing.List[str],
        skip_anonymous: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Replace previous blackbox patterns with passed ones. Forces backend to skip stepping/pausing in
    scripts with url matching one of the patterns. VM will try to leave blackboxed script by
    performing 'step in' several times, finally resorting to 'step out' if unsuccessful.

    **EXPERIMENTAL**

    :param patterns: Array of regexps that will be used to check script url for blackbox state.
    :param skip_anonymous: *(Optional)* If true, also ignore scripts with no source url.
    '''
    params: T_JSON_DICT = dict()
    params['patterns'] = [i for i in patterns]
    if skip_anonymous is not None:
        params['skipAnonymous'] = skip_anonymous
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setBlackboxPatterns',
        'params': params,
    }
    json = yield cmd_dict


def set_blackboxed_ranges(
        script_id: runtime.ScriptId,
        positions: typing.List[ScriptPosition]
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Makes backend skip steps in the script in blackboxed ranges. VM will try leave blacklisted
    scripts by performing 'step in' several times, finally resorting to 'step out' if unsuccessful.
    Positions array contains positions where blackbox state is changed. First interval isn't
    blackboxed. Array should be sorted.

    **EXPERIMENTAL**

    :param script_id: Id of the script.
    :param positions:
    '''
    params: T_JSON_DICT = dict()
    params['scriptId'] = script_id.to_json()
    params['positions'] = [i.to_json() for i in positions]
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setBlackboxedRanges',
        'params': params,
    }
    json = yield cmd_dict


def set_breakpoint(
        location: Location,
        condition: typing.Optional[str] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[BreakpointId, Location]]:
    '''
    Sets JavaScript breakpoint at a given location.

    :param location: Location to set breakpoint in.
    :param condition: *(Optional)* Expression to use as a breakpoint condition. When specified, debugger will only stop on the breakpoint if this expression evaluates to true.
    :returns: A tuple with the following items:

        0. **breakpointId** - Id of the created breakpoint for further reference.
        1. **actualLocation** - Location this breakpoint resolved into.
    '''
    params: T_JSON_DICT = dict()
    params['location'] = location.to_json()
    if condition is not None:
        params['condition'] = condition
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setBreakpoint',
        'params': params,
    }
    json = yield cmd_dict
    return (
        BreakpointId.from_json(json['breakpointId']),
        Location.from_json(json['actualLocation'])
    )


def set_instrumentation_breakpoint(
        instrumentation: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,BreakpointId]:
    '''
    Sets instrumentation breakpoint.

    :param instrumentation: Instrumentation name.
    :returns: Id of the created breakpoint for further reference.
    '''
    params: T_JSON_DICT = dict()
    params['instrumentation'] = instrumentation
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setInstrumentationBreakpoint',
        'params': params,
    }
    json = yield cmd_dict
    return BreakpointId.from_json(json['breakpointId'])


def set_breakpoint_by_url(
        line_number: int,
        url: typing.Optional[str] = None,
        url_regex: typing.Optional[str] = None,
        script_hash: typing.Optional[str] = None,
        column_number: typing.Optional[int] = None,
        condition: typing.Optional[str] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[BreakpointId, typing.List[Location]]]:
    '''
    Sets JavaScript breakpoint at given location specified either by URL or URL regex. Once this
    command is issued, all existing parsed scripts will have breakpoints resolved and returned in
    ``locations`` property. Further matching script parsing will result in subsequent
    ``breakpointResolved`` events issued. This logical breakpoint will survive page reloads.

    :param line_number: Line number to set breakpoint at.
    :param url: *(Optional)* URL of the resources to set breakpoint on.
    :param url_regex: *(Optional)* Regex pattern for the URLs of the resources to set breakpoints on. Either ```url```` or ````urlRegex``` must be specified.
    :param script_hash: *(Optional)* Script hash of the resources to set breakpoint on.
    :param column_number: *(Optional)* Offset in the line to set breakpoint at.
    :param condition: *(Optional)* Expression to use as a breakpoint condition. When specified, debugger will only stop on the breakpoint if this expression evaluates to true.
    :returns: A tuple with the following items:

        0. **breakpointId** - Id of the created breakpoint for further reference.
        1. **locations** - List of the locations this breakpoint resolved into upon addition.
    '''
    params: T_JSON_DICT = dict()
    params['lineNumber'] = line_number
    if url is not None:
        params['url'] = url
    if url_regex is not None:
        params['urlRegex'] = url_regex
    if script_hash is not None:
        params['scriptHash'] = script_hash
    if column_number is not None:
        params['columnNumber'] = column_number
    if condition is not None:
        params['condition'] = condition
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setBreakpointByUrl',
        'params': params,
    }
    json = yield cmd_dict
    return (
        BreakpointId.from_json(json['breakpointId']),
        [Location.from_json(i) for i in json['locations']]
    )


def set_breakpoint_on_function_call(
        object_id: runtime.RemoteObjectId,
        condition: typing.Optional[str] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,BreakpointId]:
    '''
    Sets JavaScript breakpoint before each call to the given function.
    If another function was created from the same source as a given one,
    calling it will also trigger the breakpoint.

    **EXPERIMENTAL**

    :param object_id: Function object id.
    :param condition: *(Optional)* Expression to use as a breakpoint condition. When specified, debugger will stop on the breakpoint if this expression evaluates to true.
    :returns: Id of the created breakpoint for further reference.
    '''
    params: T_JSON_DICT = dict()
    params['objectId'] = object_id.to_json()
    if condition is not None:
        params['condition'] = condition
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setBreakpointOnFunctionCall',
        'params': params,
    }
    json = yield cmd_dict
    return BreakpointId.from_json(json['breakpointId'])


def set_breakpoints_active(
        active: bool
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Activates / deactivates all breakpoints on the page.

    :param active: New value for breakpoints active state.
    '''
    params: T_JSON_DICT = dict()
    params['active'] = active
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setBreakpointsActive',
        'params': params,
    }
    json = yield cmd_dict


def set_pause_on_exceptions(
        state: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Defines pause on exceptions state. Can be set to stop on all exceptions, uncaught exceptions,
    or caught exceptions, no exceptions. Initial pause on exceptions state is ``none``.

    :param state: Pause on exceptions mode.
    '''
    params: T_JSON_DICT = dict()
    params['state'] = state
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setPauseOnExceptions',
        'params': params,
    }
    json = yield cmd_dict


def set_return_value(
        new_value: runtime.CallArgument
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Changes return value in top frame. Available only at return break position.

    **EXPERIMENTAL**

    :param new_value: New return value.
    '''
    params: T_JSON_DICT = dict()
    params['newValue'] = new_value.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setReturnValue',
        'params': params,
    }
    json = yield cmd_dict


def set_script_source(
        script_id: runtime.ScriptId,
        script_source: str,
        dry_run: typing.Optional[bool] = None,
        allow_top_frame_editing: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[typing.Optional[typing.List[CallFrame]], typing.Optional[bool], typing.Optional[runtime.StackTrace], typing.Optional[runtime.StackTraceId], str, typing.Optional[runtime.ExceptionDetails]]]:
    '''
    Edits JavaScript source live.

    In general, functions that are currently on the stack can not be edited with
    a single exception: If the edited function is the top-most stack frame and
    that is the only activation of that function on the stack. In this case
    the live edit will be successful and a ``Debugger.restartFrame`` for the
    top-most function is automatically triggered.

    :param script_id: Id of the script to edit.
    :param script_source: New content of the script.
    :param dry_run: *(Optional)* If true the change will not actually be applied. Dry run may be used to get result description without actually modifying the code.
    :param allow_top_frame_editing: **(EXPERIMENTAL)** *(Optional)* If true, then ```scriptSource```` is allowed to change the function on top of the stack as long as the top-most stack frame is the only activation of that function.
    :returns: A tuple with the following items:

        0. **callFrames** - *(Optional)* New stack trace in case editing has happened while VM was stopped.
        1. **stackChanged** - *(Optional)* Whether current call stack  was modified after applying the changes.
        2. **asyncStackTrace** - *(Optional)* Async stack trace, if any.
        3. **asyncStackTraceId** - *(Optional)* Async stack trace, if any.
        4. **status** - Whether the operation was successful or not. Only `` Ok`` denotes a successful live edit while the other enum variants denote why the live edit failed.
        5. **exceptionDetails** - *(Optional)* Exception details if any. Only present when `` status`` is `` CompileError`.
    '''
    params: T_JSON_DICT = dict()
    params['scriptId'] = script_id.to_json()
    params['scriptSource'] = script_source
    if dry_run is not None:
        params['dryRun'] = dry_run
    if allow_top_frame_editing is not None:
        params['allowTopFrameEditing'] = allow_top_frame_editing
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setScriptSource',
        'params': params,
    }
    json = yield cmd_dict
    return (
        [CallFrame.from_json(i) for i in json['callFrames']] if 'callFrames' in json else None,
        bool(json['stackChanged']) if 'stackChanged' in json else None,
        runtime.StackTrace.from_json(json['asyncStackTrace']) if 'asyncStackTrace' in json else None,
        runtime.StackTraceId.from_json(json['asyncStackTraceId']) if 'asyncStackTraceId' in json else None,
        str(json['status']),
        runtime.ExceptionDetails.from_json(json['exceptionDetails']) if 'exceptionDetails' in json else None
    )


def set_skip_all_pauses(
        skip: bool
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Makes page not interrupt on any pauses (breakpoint, exception, dom exception etc).

    :param skip: New value for skip pauses state.
    '''
    params: T_JSON_DICT = dict()
    params['skip'] = skip
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setSkipAllPauses',
        'params': params,
    }
    json = yield cmd_dict


def set_variable_value(
        scope_number: int,
        variable_name: str,
        new_value: runtime.CallArgument,
        call_frame_id: CallFrameId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Changes value of variable in a callframe. Object-based scopes are not supported and must be
    mutated manually.

    :param scope_number: 0-based number of scope as was listed in scope chain. Only 'local', 'closure' and 'catch' scope types are allowed. Other scopes could be manipulated manually.
    :param variable_name: Variable name.
    :param new_value: New variable value.
    :param call_frame_id: Id of callframe that holds variable.
    '''
    params: T_JSON_DICT = dict()
    params['scopeNumber'] = scope_number
    params['variableName'] = variable_name
    params['newValue'] = new_value.to_json()
    params['callFrameId'] = call_frame_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.setVariableValue',
        'params': params,
    }
    json = yield cmd_dict


def step_into(
        break_on_async_call: typing.Optional[bool] = None,
        skip_list: typing.Optional[typing.List[LocationRange]] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Steps into the function call.

    :param break_on_async_call: **(EXPERIMENTAL)** *(Optional)* Debugger will pause on the execution of the first async task which was scheduled before next pause.
    :param skip_list: **(EXPERIMENTAL)** *(Optional)* The skipList specifies location ranges that should be skipped on step into.
    '''
    params: T_JSON_DICT = dict()
    if break_on_async_call is not None:
        params['breakOnAsyncCall'] = break_on_async_call
    if skip_list is not None:
        params['skipList'] = [i.to_json() for i in skip_list]
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.stepInto',
        'params': params,
    }
    json = yield cmd_dict


def step_out() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Steps out of the function call.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.stepOut',
    }
    json = yield cmd_dict


def step_over(
        skip_list: typing.Optional[typing.List[LocationRange]] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Steps over the statement.

    :param skip_list: **(EXPERIMENTAL)** *(Optional)* The skipList specifies location ranges that should be skipped on step over.
    '''
    params: T_JSON_DICT = dict()
    if skip_list is not None:
        params['skipList'] = [i.to_json() for i in skip_list]
    cmd_dict: T_JSON_DICT = {
        'method': 'Debugger.stepOver',
        'params': params,
    }
    json = yield cmd_dict


@event_class('Debugger.breakpointResolved')
@dataclass
class BreakpointResolved:
    '''
    Fired when breakpoint is resolved to an actual script and location.
    Deprecated in favor of ``resolvedBreakpoints`` in the ``scriptParsed`` event.
    '''
    #: Breakpoint unique identifier.
    breakpoint_id: BreakpointId
    #: Actual breakpoint location.
    location: Location

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> BreakpointResolved:
        return cls(
            breakpoint_id=BreakpointId.from_json(json['breakpointId']),
            location=Location.from_json(json['location'])
        )


@event_class('Debugger.paused')
@dataclass
class Paused:
    '''
    Fired when the virtual machine stopped on breakpoint or exception or any other stop criteria.
    '''
    #: Call stack the virtual machine stopped on.
    call_frames: typing.List[CallFrame]
    #: Pause reason.
    reason: str
    #: Object containing break-specific auxiliary properties.
    data: typing.Optional[dict]
    #: Hit breakpoints IDs
    hit_breakpoints: typing.Optional[typing.List[str]]
    #: Async stack trace, if any.
    async_stack_trace: typing.Optional[runtime.StackTrace]
    #: Async stack trace, if any.
    async_stack_trace_id: typing.Optional[runtime.StackTraceId]
    #: Never present, will be removed.
    async_call_stack_trace_id: typing.Optional[runtime.StackTraceId]

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> Paused:
        return cls(
            call_frames=[CallFrame.from_json(i) for i in json['callFrames']],
            reason=str(json['reason']),
            data=dict(json['data']) if 'data' in json else None,
            hit_breakpoints=[str(i) for i in json['hitBreakpoints']] if 'hitBreakpoints' in json else None,
            async_stack_trace=runtime.StackTrace.from_json(json['asyncStackTrace']) if 'asyncStackTrace' in json else None,
            async_stack_trace_id=runtime.StackTraceId.from_json(json['asyncStackTraceId']) if 'asyncStackTraceId' in json else None,
            async_call_stack_trace_id=runtime.StackTraceId.from_json(json['asyncCallStackTraceId']) if 'asyncCallStackTraceId' in json else None
        )


@event_class('Debugger.resumed')
@dataclass
class Resumed:
    '''
    Fired when the virtual machine resumed execution.
    '''


    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> Resumed:
        return cls(

        )


@event_class('Debugger.scriptFailedToParse')
@dataclass
class ScriptFailedToParse:
    '''
    Fired when virtual machine fails to parse the script.
    '''
    #: Identifier of the script parsed.
    script_id: runtime.ScriptId
    #: URL or name of the script parsed (if any).
    url: str
    #: Line offset of the script within the resource with given URL (for script tags).
    start_line: int
    #: Column offset of the script within the resource with given URL.
    start_column: int
    #: Last line of the script.
    end_line: int
    #: Length of the last line of the script.
    end_column: int
    #: Specifies script creation context.
    execution_context_id: runtime.ExecutionContextId
    #: Content hash of the script, SHA-256.
    hash_: str
    #: For Wasm modules, the content of the ``build_id`` custom section. For JavaScript the ``debugId`` magic comment.
    build_id: str
    #: Embedder-specific auxiliary data likely matching {isDefault: boolean, type: 'default'``'isolated'``'worker', frameId: string}
    execution_context_aux_data: typing.Optional[dict]
    #: URL of source map associated with script (if any).
    source_map_url: typing.Optional[str]
    #: True, if this script has sourceURL.
    has_source_url: typing.Optional[bool]
    #: True, if this script is ES6 module.
    is_module: typing.Optional[bool]
    #: This script length.
    length: typing.Optional[int]
    #: JavaScript top stack frame of where the script parsed event was triggered if available.
    stack_trace: typing.Optional[runtime.StackTrace]
    #: If the scriptLanguage is WebAssembly, the code section offset in the module.
    code_offset: typing.Optional[int]
    #: The language of the script.
    script_language: typing.Optional[debugger.ScriptLanguage]
    #: The name the embedder supplied for this script.
    embedder_name: typing.Optional[str]

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ScriptFailedToParse:
        return cls(
            script_id=runtime.ScriptId.from_json(json['scriptId']),
            url=str(json['url']),
            start_line=int(json['startLine']),
            start_column=int(json['startColumn']),
            end_line=int(json['endLine']),
            end_column=int(json['endColumn']),
            execution_context_id=runtime.ExecutionContextId.from_json(json['executionContextId']),
            hash_=str(json['hash']),
            build_id=str(json['buildId']),
            execution_context_aux_data=dict(json['executionContextAuxData']) if 'executionContextAuxData' in json else None,
            source_map_url=str(json['sourceMapURL']) if 'sourceMapURL' in json else None,
            has_source_url=bool(json['hasSourceURL']) if 'hasSourceURL' in json else None,
            is_module=bool(json['isModule']) if 'isModule' in json else None,
            length=int(json['length']) if 'length' in json else None,
            stack_trace=runtime.StackTrace.from_json(json['stackTrace']) if 'stackTrace' in json else None,
            code_offset=int(json['codeOffset']) if 'codeOffset' in json else None,
            script_language=debugger.ScriptLanguage.from_json(json['scriptLanguage']) if 'scriptLanguage' in json else None,
            embedder_name=str(json['embedderName']) if 'embedderName' in json else None
        )


@event_class('Debugger.scriptParsed')
@dataclass
class ScriptParsed:
    '''
    Fired when virtual machine parses script. This event is also fired for all known and uncollected
    scripts upon enabling debugger.
    '''
    #: Identifier of the script parsed.
    script_id: runtime.ScriptId
    #: URL or name of the script parsed (if any).
    url: str
    #: Line offset of the script within the resource with given URL (for script tags).
    start_line: int
    #: Column offset of the script within the resource with given URL.
    start_column: int
    #: Last line of the script.
    end_line: int
    #: Length of the last line of the script.
    end_column: int
    #: Specifies script creation context.
    execution_context_id: runtime.ExecutionContextId
    #: Content hash of the script, SHA-256.
    hash_: str
    #: For Wasm modules, the content of the ``build_id`` custom section. For JavaScript the ``debugId`` magic comment.
    build_id: str
    #: Embedder-specific auxiliary data likely matching {isDefault: boolean, type: 'default'``'isolated'``'worker', frameId: string}
    execution_context_aux_data: typing.Optional[dict]
    #: True, if this script is generated as a result of the live edit operation.
    is_live_edit: typing.Optional[bool]
    #: URL of source map associated with script (if any).
    source_map_url: typing.Optional[str]
    #: True, if this script has sourceURL.
    has_source_url: typing.Optional[bool]
    #: True, if this script is ES6 module.
    is_module: typing.Optional[bool]
    #: This script length.
    length: typing.Optional[int]
    #: JavaScript top stack frame of where the script parsed event was triggered if available.
    stack_trace: typing.Optional[runtime.StackTrace]
    #: If the scriptLanguage is WebAssembly, the code section offset in the module.
    code_offset: typing.Optional[int]
    #: The language of the script.
    script_language: typing.Optional[debugger.ScriptLanguage]
    #: If the scriptLanguage is WebAssembly, the source of debug symbols for the module.
    debug_symbols: typing.Optional[typing.List[debugger.DebugSymbols]]
    #: The name the embedder supplied for this script.
    embedder_name: typing.Optional[str]
    #: The list of set breakpoints in this script if calls to ``setBreakpointByUrl``
    #: matches this script's URL or hash. Clients that use this list can ignore the
    #: ``breakpointResolved`` event. They are equivalent.
    resolved_breakpoints: typing.Optional[typing.List[ResolvedBreakpoint]]

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ScriptParsed:
        return cls(
            script_id=runtime.ScriptId.from_json(json['scriptId']),
            url=str(json['url']),
            start_line=int(json['startLine']),
            start_column=int(json['startColumn']),
            end_line=int(json['endLine']),
            end_column=int(json['endColumn']),
            execution_context_id=runtime.ExecutionContextId.from_json(json['executionContextId']),
            hash_=str(json['hash']),
            build_id=str(json['buildId']),
            execution_context_aux_data=dict(json['executionContextAuxData']) if 'executionContextAuxData' in json else None,
            is_live_edit=bool(json['isLiveEdit']) if 'isLiveEdit' in json else None,
            source_map_url=str(json['sourceMapURL']) if 'sourceMapURL' in json else None,
            has_source_url=bool(json['hasSourceURL']) if 'hasSourceURL' in json else None,
            is_module=bool(json['isModule']) if 'isModule' in json else None,
            length=int(json['length']) if 'length' in json else None,
            stack_trace=runtime.StackTrace.from_json(json['stackTrace']) if 'stackTrace' in json else None,
            code_offset=int(json['codeOffset']) if 'codeOffset' in json else None,
            script_language=debugger.ScriptLanguage.from_json(json['scriptLanguage']) if 'scriptLanguage' in json else None,
            debug_symbols=[debugger.DebugSymbols.from_json(i) for i in json['debugSymbols']] if 'debugSymbols' in json else None,
            embedder_name=str(json['embedderName']) if 'embedderName' in json else None,
            resolved_breakpoints=[ResolvedBreakpoint.from_json(i) for i in json['resolvedBreakpoints']] if 'resolvedBreakpoints' in json else None
        )
