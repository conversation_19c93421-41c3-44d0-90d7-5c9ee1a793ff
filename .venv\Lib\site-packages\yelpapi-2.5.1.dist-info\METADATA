Metadata-Version: 2.1
Name: yelpapi
Version: 2.5.1
Summary: yelpapi is a pure Python implementation of the Yelp Fusion API.
Home-page: https://github.com/lanl/yelpapi
Author: <PERSON>
Author-email: <EMAIL>
Maintainer: <PERSON>-email: <EMAIL>
License: BSD 3-Clause License
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Classifier: Topic :: Software Development :: Libraries :: Python Modules
License-File: LICENSE
License-File: AUTHORS.md
Requires-Dist: requests

yelpapi is a pure Python implementation of the `Yelp Fusion API <https://docs.developer.yelp.com/docs/fusion-intro>`_. It is simple, fast, and robust to any changes Ye<PERSON><PERSON> may make to the API in the future.

For more information on yelpapi, visit the `GitHub project page <https://github.com/lanl/yelpapi>`_.
