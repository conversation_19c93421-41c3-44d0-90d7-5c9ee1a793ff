#!/usr/bin/env python3
"""
Email Scraper for Coffee Business Websites
Extracts email addresses from business websites and updates the CSV file
"""
import csv
import re
import time
import requests
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import pandas as pd
from email_validator import validate_email, EmailNotValidError
from datetime import datetime
import os


class EmailScraper:
    """Scrapes email addresses from business websites"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.timeout = 10
        self.max_pages_per_site = 3
        
        # Improved email patterns based on research
        self.email_patterns = [
            # Standard email pattern (most comprehensive)
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            # Mailto links
            r'mailto:([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            # Encoded emails (URL encoded)
            r'mailto:([A-Za-z0-9._%+-]*%[0-9A-Fa-f]{2}[A-Za-z0-9._%+-]*@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            # Obfuscated emails with spaces or dots
            r'\b[A-Za-z0-9._%+-]+\s*@\s*[A-Za-z0-9.-]+\s*\.\s*[A-Z|a-z]{2,}\b',
        ]
        
        # Excluded domains (generic/non-business emails)
        self.excluded_domains = {
            'example.com', 'test.com', 'gmail.com', 'yahoo.com', 'hotmail.com',
            'outlook.com', 'aol.com', 'icloud.com', 'live.com', 'msn.com',
            'googlemail.com', 'ymail.com', 'mail.com', 'protonmail.com'
        }
        
        # Comprehensive contact page paths based on research
        self.contact_paths = [
            '/contact', '/contact-us', '/contact.html', '/contactus', '/contact.php',
            '/about', '/about-us', '/about.html', '/aboutus', '/about.php',
            '/info', '/information', '/get-in-touch', '/reach-us', '/reach-out',
            '/support', '/help', '/customer-service', '/customer-support',
            '/team', '/staff', '/people', '/leadership', '/management',
            '/locations', '/location', '/store-info', '/hours',
            '/feedback', '/inquiries', '/questions', '/faq'
        ]

        # Social media platforms and their URL patterns
        self.social_media_patterns = {
            'facebook': [
                r'https?://(?:www\.)?facebook\.com/[A-Za-z0-9._-]+',
                r'https?://(?:www\.)?fb\.com/[A-Za-z0-9._-]+',
                r'https?://(?:m\.)?facebook\.com/[A-Za-z0-9._-]+'
            ],
            'instagram': [
                r'https?://(?:www\.)?instagram\.com/[A-Za-z0-9._-]+',
                r'https?://(?:www\.)?instagr\.am/[A-Za-z0-9._-]+'
            ],
            'twitter': [
                r'https?://(?:www\.)?twitter\.com/[A-Za-z0-9._-]+',
                r'https?://(?:www\.)?x\.com/[A-Za-z0-9._-]+'
            ],
            'linkedin': [
                r'https?://(?:www\.)?linkedin\.com/(?:in|company)/[A-Za-z0-9._-]+',
                r'https?://(?:www\.)?linkedin\.com/pub/[A-Za-z0-9._-]+'
            ],
            'youtube': [
                r'https?://(?:www\.)?youtube\.com/(?:channel|user|c)/[A-Za-z0-9._-]+',
                r'https?://(?:www\.)?youtu\.be/[A-Za-z0-9._-]+'
            ],
            'tiktok': [
                r'https?://(?:www\.)?tiktok\.com/@[A-Za-z0-9._-]+'
            ],
            'pinterest': [
                r'https?://(?:www\.)?pinterest\.com/[A-Za-z0-9._-]+'
            ],
            'snapchat': [
                r'https?://(?:www\.)?snapchat\.com/add/[A-Za-z0-9._-]+'
            ],
            'yelp': [
                r'https?://(?:www\.)?yelp\.com/biz/[A-Za-z0-9._-]+'
            ],
            'google_business': [
                r'https?://(?:www\.)?google\.com/maps/place/[^/]+/@[0-9.-]+,[0-9.-]+',
                r'https?://maps\.google\.com/\?cid=[0-9]+'
            ]
        }
    
    def clean_url(self, url):
        """Clean and normalize URL"""
        if not url:
            return None

        url = url.strip()
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        # Remove URL parameters that might cause issues
        if '?' in url:
            url = url.split('?')[0]

        return url

    def detect_social_media_platform(self, url):
        """Detect if a URL is a social media platform"""
        if not url:
            return None

        url_lower = url.lower()

        if 'facebook.com' in url_lower or 'fb.com' in url_lower:
            return 'facebook'
        elif 'instagram.com' in url_lower or 'instagr.am' in url_lower:
            return 'instagram'
        elif 'twitter.com' in url_lower or 'x.com' in url_lower:
            return 'twitter'
        elif 'linkedin.com' in url_lower:
            return 'linkedin'
        elif 'youtube.com' in url_lower or 'youtu.be' in url_lower:
            return 'youtube'
        elif 'tiktok.com' in url_lower:
            return 'tiktok'
        elif 'pinterest.com' in url_lower:
            return 'pinterest'
        elif 'yelp.com' in url_lower:
            return 'yelp'

        return None
    
    def validate_email(self, email):
        """Validate email address"""
        try:
            email = email.lower().strip()
            
            # Skip obviously invalid emails
            if not email or '@' not in email:
                return None
                
            # Check domain exclusions
            domain = email.split('@')[1]
            if domain in self.excluded_domains:
                return None
                
            # Skip common non-business patterns
            invalid_patterns = [
                r'noreply', r'no-reply', r'donotreply', r'support@',
                r'admin@', r'webmaster@', r'postmaster@', r'abuse@'
            ]
            
            for pattern in invalid_patterns:
                if re.search(pattern, email, re.IGNORECASE):
                    return None
            
            # Validate with email-validator
            validated = validate_email(email)
            return validated.email
            
        except (EmailNotValidError, Exception):
            return None
    
    def decode_cloudflare_email(self, encoded_string):
        """Decode Cloudflare obfuscated emails"""
        try:
            if len(encoded_string) < 2:
                return None
            r = int(encoded_string[:2], 16)
            email = ''.join([chr(int(encoded_string[i:i+2], 16) ^ r) for i in range(2, len(encoded_string), 2)])
            return email
        except:
            return None

    def extract_emails_from_text(self, text):
        """Extract email addresses from text content with advanced techniques"""
        emails = set()

        # Standard regex patterns
        for pattern in self.email_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0] if match else ''

                # Handle URL encoded emails
                if '%' in match:
                    try:
                        from urllib.parse import unquote
                        match = unquote(match)
                    except:
                        pass

                # Clean up spaces in obfuscated emails
                match = re.sub(r'\s+', '', match)

                validated_email = self.validate_email(match)
                if validated_email:
                    emails.add(validated_email)

        return list(emails)

    def scrape_social_media_for_emails(self, url, platform, business_name):
        """Scrape social media pages for email addresses"""
        print(f"    📱 Detected {platform} page, extracting emails...")

        try:
            # Use different strategies for different platforms
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            }

            response = self.session.get(url, timeout=self.timeout, headers=headers)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')
            text = soup.get_text()

            emails = []

            if platform == 'facebook':
                emails.extend(self._extract_facebook_emails(soup, text))
            elif platform == 'instagram':
                emails.extend(self._extract_instagram_emails(soup, text))
            elif platform == 'twitter':
                emails.extend(self._extract_twitter_emails(soup, text))
            elif platform == 'linkedin':
                emails.extend(self._extract_linkedin_emails(soup, text))
            elif platform == 'youtube':
                emails.extend(self._extract_youtube_emails(soup, text))
            else:
                # Generic email extraction
                emails.extend(self.extract_emails_from_text(text))

            # Remove duplicates and validate
            unique_emails = []
            for email in emails:
                validated = self.validate_email(email)
                if validated and validated not in unique_emails:
                    unique_emails.append(validated)

            return unique_emails

        except Exception as e:
            print(f"    ⚠️ Error scraping {platform}: {e}")
            return []

    def _extract_facebook_emails(self, soup, text):
        """Extract emails from Facebook pages"""
        emails = []

        # Look for contact info in various Facebook page sections
        # Facebook often puts contact info in specific divs
        contact_selectors = [
            '[data-testid*="contact"]',
            '[data-testid*="email"]',
            '.x1i10hfl',  # Common Facebook link class
            '.x1emribx',  # Another common class
            'div[role="main"]',  # Main content area
        ]

        for selector in contact_selectors:
            elements = soup.select(selector)
            for element in elements:
                emails.extend(self.extract_emails_from_text(element.get_text()))

        # Look for mailto links
        mailto_links = soup.find_all('a', href=re.compile(r'^mailto:', re.I))
        for link in mailto_links:
            href = link.get('href', '')
            if href.startswith('mailto:'):
                email = href[7:].split('?')[0]
                emails.append(email)

        # Generic text search as fallback
        emails.extend(self.extract_emails_from_text(text))

        return emails

    def _extract_instagram_emails(self, soup, text):
        """Extract emails from Instagram profiles"""
        emails = []

        # Instagram bio and contact info selectors
        bio_selectors = [
            'div[data-testid="user-bio"]',
            '.x1lliihq',  # Common Instagram text class
            '.x193iq5w',  # Another common class
            'span[dir="auto"]',  # Bio text spans
            'div[role="main"]',  # Main content
        ]

        for selector in bio_selectors:
            elements = soup.select(selector)
            for element in elements:
                emails.extend(self.extract_emails_from_text(element.get_text()))

        # Look for contact buttons or links
        contact_elements = soup.find_all(['a', 'button'], string=re.compile(r'contact|email', re.I))
        for element in contact_elements:
            emails.extend(self.extract_emails_from_text(element.get_text()))
            # Check href if it's a link
            if element.name == 'a' and element.get('href'):
                href = element.get('href')
                if 'mailto:' in href:
                    email = href.split('mailto:')[1].split('?')[0]
                    emails.append(email)

        # Generic text search
        emails.extend(self.extract_emails_from_text(text))

        return emails

    def _extract_twitter_emails(self, soup, text):
        """Extract emails from Twitter/X profiles"""
        emails = []

        # Twitter bio and profile selectors
        bio_selectors = [
            '[data-testid="UserDescription"]',
            '[data-testid="UserProfileHeader_Items"]',
            '.css-901oao',  # Common Twitter text class
            'div[dir="auto"]',  # Bio text
        ]

        for selector in bio_selectors:
            elements = soup.select(selector)
            for element in elements:
                emails.extend(self.extract_emails_from_text(element.get_text()))

        # Generic text search
        emails.extend(self.extract_emails_from_text(text))

        return emails

    def _extract_linkedin_emails(self, soup, text):
        """Extract emails from LinkedIn profiles"""
        emails = []

        # LinkedIn contact info selectors
        contact_selectors = [
            '.pv-contact-info',
            '.ci-email',
            '.contact-info',
            '.pv-profile-section__section-info',
        ]

        for selector in contact_selectors:
            elements = soup.select(selector)
            for element in elements:
                emails.extend(self.extract_emails_from_text(element.get_text()))

        # Generic text search
        emails.extend(self.extract_emails_from_text(text))

        return emails

    def _extract_youtube_emails(self, soup, text):
        """Extract emails from YouTube channels"""
        emails = []

        # YouTube channel description selectors
        desc_selectors = [
            '#description',
            '.about-description',
            '.channel-header-links',
            '.ytd-channel-about-metadata-renderer',
        ]

        for selector in desc_selectors:
            elements = soup.select(selector)
            for element in elements:
                emails.extend(self.extract_emails_from_text(element.get_text()))

        # Generic text search
        emails.extend(self.extract_emails_from_text(text))

        return emails

    def extract_social_media_links(self, text, soup):
        """Extract social media links from text and HTML"""
        social_links = {}

        # Initialize all platforms
        for platform in self.social_media_patterns:
            social_links[platform] = []

        # Search in text content
        for platform, patterns in self.social_media_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches:
                    # Clean up the URL
                    clean_url = match.split('?')[0]  # Remove query parameters
                    if clean_url not in social_links[platform]:
                        social_links[platform].append(clean_url)

        # Search in href attributes
        if soup:
            all_links = soup.find_all('a', href=True)
            for link in all_links:
                href = link.get('href', '')
                for platform, patterns in self.social_media_patterns.items():
                    for pattern in patterns:
                        if re.match(pattern, href, re.IGNORECASE):
                            clean_url = href.split('?')[0]
                            if clean_url not in social_links[platform]:
                                social_links[platform].append(clean_url)

        # Remove empty lists
        social_links = {k: v for k, v in social_links.items() if v}

        return social_links

    def scrape_multiple_social_media_for_emails(self, social_links, business_name):
        """Scrape multiple social media profiles for email addresses"""
        social_emails = []

        for platform, links in social_links.items():
            for link in links:
                try:
                    print(f"    🔍 Checking {platform}: {link}")

                    # Add headers to appear more like a real browser
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.5',
                        'Accept-Encoding': 'gzip, deflate',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                    }

                    response = self.session.get(link, timeout=self.timeout, headers=headers)
                    response.raise_for_status()

                    soup = BeautifulSoup(response.content, 'html.parser')

                    # Platform-specific email extraction strategies
                    emails = []

                    if platform == 'facebook':
                        emails.extend(self._extract_facebook_emails(soup, response.text))
                    elif platform == 'instagram':
                        emails.extend(self._extract_instagram_emails(soup, response.text))
                    elif platform == 'twitter':
                        emails.extend(self._extract_twitter_emails(soup, response.text))
                    elif platform == 'linkedin':
                        emails.extend(self._extract_linkedin_emails(soup, response.text))
                    elif platform == 'youtube':
                        emails.extend(self._extract_youtube_emails(soup, response.text))
                    else:
                        # Generic email extraction for other platforms
                        emails.extend(self.extract_emails_from_text(response.text))

                    if emails:
                        print(f"      ✅ Found emails on {platform}: {', '.join(emails)}")
                        social_emails.extend(emails)
                    else:
                        print(f"      ❌ No emails found on {platform}")

                    # Rate limiting - be respectful to social media platforms
                    time.sleep(2)

                except Exception as e:
                    print(f"      ⚠️ Error checking {platform}: {e}")
                    continue

        # Remove duplicates and validate
        unique_emails = []
        for email in social_emails:
            validated = self.validate_email(email)
            if validated and validated not in unique_emails:
                unique_emails.append(validated)

        return unique_emails

    def _extract_facebook_emails(self, soup, text):
        """Extract emails from Facebook pages"""
        emails = []

        # Look for contact info sections
        contact_sections = soup.find_all(['div', 'span'], class_=re.compile(r'contact|email|info', re.I))
        for section in contact_sections:
            emails.extend(self.extract_emails_from_text(section.get_text()))

        # Look for about sections
        about_sections = soup.find_all(['div', 'span'], class_=re.compile(r'about|bio|description', re.I))
        for section in about_sections:
            emails.extend(self.extract_emails_from_text(section.get_text()))

        # Generic text search
        emails.extend(self.extract_emails_from_text(text))

        return emails

    def _extract_instagram_emails(self, soup, text):
        """Extract emails from Instagram profiles"""
        emails = []

        # Look for bio sections
        bio_sections = soup.find_all(['div', 'span'], class_=re.compile(r'bio|description|contact', re.I))
        for section in bio_sections:
            emails.extend(self.extract_emails_from_text(section.get_text()))

        # Look for link in bio
        link_sections = soup.find_all('a', href=True)
        for link in link_sections:
            href = link.get('href', '')
            if 'mailto:' in href:
                email = href.replace('mailto:', '').split('?')[0]
                emails.append(email)

        # Generic text search
        emails.extend(self.extract_emails_from_text(text))

        return emails

    def _extract_twitter_emails(self, soup, text):
        """Extract emails from Twitter/X profiles"""
        emails = []

        # Look for bio/description sections
        bio_sections = soup.find_all(['div', 'span'], class_=re.compile(r'bio|description|profile', re.I))
        for section in bio_sections:
            emails.extend(self.extract_emails_from_text(section.get_text()))

        # Generic text search
        emails.extend(self.extract_emails_from_text(text))

        return emails

    def _extract_linkedin_emails(self, soup, text):
        """Extract emails from LinkedIn profiles"""
        emails = []

        # Look for contact info sections
        contact_sections = soup.find_all(['div', 'span'], class_=re.compile(r'contact|email|info', re.I))
        for section in contact_sections:
            emails.extend(self.extract_emails_from_text(section.get_text()))

        # Generic text search
        emails.extend(self.extract_emails_from_text(text))

        return emails

    def _extract_youtube_emails(self, soup, text):
        """Extract emails from YouTube channels"""
        emails = []

        # Look for channel description/about sections
        about_sections = soup.find_all(['div', 'span'], class_=re.compile(r'about|description|channel', re.I))
        for section in about_sections:
            emails.extend(self.extract_emails_from_text(section.get_text()))

        # Generic text search
        emails.extend(self.extract_emails_from_text(text))

        return emails
    
    def scrape_page_for_emails(self, url):
        """Scrape a single page for email addresses with advanced techniques"""
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()

            # Parse HTML content
            soup = BeautifulSoup(response.content, 'html.parser')

            # Check for Cloudflare email obfuscation
            cf_emails = soup.find_all('span', class_='__cf_email__')
            cloudflare_emails = []
            for cf_email in cf_emails:
                encoded = cf_email.get('data-cfemail', '')
                if encoded:
                    decoded = self.decode_cloudflare_email(encoded)
                    if decoded:
                        cloudflare_emails.append(decoded)

            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()

            # Get text content
            text = soup.get_text()

            # Check href attributes for mailto links
            mailto_links = soup.find_all('a', href=re.compile(r'^mailto:', re.I))
            for link in mailto_links:
                href = link.get('href', '')
                if href.startswith('mailto:'):
                    email = href[7:].split('?')[0]  # Remove mailto: and any parameters
                    text += f" {email} "

            # Check for data attributes that might contain emails
            email_elements = soup.find_all(attrs={'data-email': True})
            for element in email_elements:
                email = element.get('data-email', '')
                if email:
                    text += f" {email} "

            # Look for common email containers
            email_containers = soup.find_all(['div', 'span', 'p'], class_=re.compile(r'email|contact|mail', re.I))
            for container in email_containers:
                text += f" {container.get_text()} "

            # Extract emails from text
            emails = self.extract_emails_from_text(text)

            # Add Cloudflare decoded emails
            emails.extend(cloudflare_emails)

            # Remove duplicates and validate emails
            unique_emails = []
            for email in emails:
                validated = self.validate_email(email)
                if validated and validated not in unique_emails:
                    unique_emails.append(validated)

            # Extract social media links
            social_links = self.extract_social_media_links(text, soup)

            return unique_emails, social_links

        except Exception as e:
            print(f"    ⚠️ Error scraping {url}: {e}")
            return [], {}
    
    def scrape_website_for_emails_and_social(self, base_url, business_name):
        """Scrape a website for email addresses and social media links"""
        print(f"  🔍 Scraping {business_name}: {base_url}")

        all_emails = set()
        all_social_links = {}
        pages_scraped = 0

        # Initialize social links structure
        for platform in self.social_media_patterns:
            all_social_links[platform] = []

        # Clean the base URL
        base_url = self.clean_url(base_url)
        if not base_url:
            return [], {}

        try:
            # Check if the main URL is actually a social media page
            social_platform = self.detect_social_media_platform(base_url)

            if social_platform:
                # This is a social media page, scrape it directly
                print(f"    🎯 Website URL is a {social_platform} page")
                emails = self.scrape_social_media_for_emails(base_url, social_platform, business_name)
                all_emails.update(emails)

                # Add the social media link to our collection
                all_social_links[social_platform].append(base_url)

                if emails:
                    print(f"    ✅ Found emails on {social_platform}: {', '.join(emails)}")
                    # Remove empty social media lists
                    all_social_links = {k: v for k, v in all_social_links.items() if v}
                    return list(all_emails), all_social_links
                else:
                    print(f"    ❌ No emails found on {social_platform} page")
                    # Still return the social link even if no emails found
                    all_social_links = {k: v for k, v in all_social_links.items() if v}
                    return list(all_emails), all_social_links

            else:
                # Regular website, scrape normally
                emails, social_links = self.scrape_page_for_emails(base_url)
                all_emails.update(emails)

                # Merge social links
                for platform, links in social_links.items():
                    all_social_links[platform].extend(links)

                pages_scraped += 1

                if emails or social_links:
                    found_items = []
                    if emails:
                        found_items.append(f"emails: {', '.join(emails)}")
                    if social_links:
                        social_summary = []
                        for platform, links in social_links.items():
                            if links:
                                social_summary.append(f"{platform}: {len(links)}")
                        if social_summary:
                            found_items.append(f"social: {', '.join(social_summary)}")

                    print(f"    ✅ Found on main page: {'; '.join(found_items)}")

                    # If we found emails, return early, otherwise continue searching
                    if emails:
                        # Remove empty social media lists
                        all_social_links = {k: v for k, v in all_social_links.items() if v}
                        return list(all_emails), all_social_links

            # If no emails found on main page, try contact pages
            parsed_url = urlparse(base_url)
            base_domain = f"{parsed_url.scheme}://{parsed_url.netloc}"

            for contact_path in self.contact_paths:
                if pages_scraped >= self.max_pages_per_site:
                    break

                contact_url = urljoin(base_domain, contact_path)

                try:
                    emails, social_links = self.scrape_page_for_emails(contact_url)
                    if emails or social_links:
                        all_emails.update(emails)

                        # Merge social links
                        for platform, links in social_links.items():
                            all_social_links[platform].extend(links)

                        pages_scraped += 1

                        found_items = []
                        if emails:
                            found_items.append(f"emails: {', '.join(emails)}")
                        if social_links:
                            social_summary = []
                            for platform, links in social_links.items():
                                if links:
                                    social_summary.append(f"{platform}: {len(links)}")
                            if social_summary:
                                found_items.append(f"social: {', '.join(social_summary)}")

                        print(f"    ✅ Found on {contact_path}: {'; '.join(found_items)}")

                        if emails:  # Stop after finding emails
                            break

                except Exception:
                    continue

                # Small delay between requests
                time.sleep(0.5)

            # Remove duplicates from social links
            for platform in all_social_links:
                all_social_links[platform] = list(set(all_social_links[platform]))

            # Remove empty social media lists
            all_social_links = {k: v for k, v in all_social_links.items() if v}

            if all_emails or all_social_links:
                return list(all_emails), all_social_links
            else:
                print(f"    ❌ No emails or social links found for {business_name}")
                return [], {}

        except Exception as e:
            print(f"    ❌ Error scraping {business_name}: {e}")
            return [], {}
    
    def process_csv_file(self, csv_file_path, max_businesses=None):
        """Process the CSV file and add email addresses"""
        print(f"📧 Starting email extraction from business websites...")
        print(f"📁 Processing file: {csv_file_path}")

        # Read the CSV file
        df = pd.read_csv(csv_file_path)
        print(f"📊 Total businesses in file: {len(df)}")

        # Count businesses with websites
        businesses_with_websites = df[df['website_url'].notna() & (df['website_url'] != '')].copy()
        print(f"🌐 Businesses with websites: {len(businesses_with_websites)}")

        # Limit for testing if specified
        if max_businesses and len(businesses_with_websites) > max_businesses:
            businesses_with_websites = businesses_with_websites.head(max_businesses)
            print(f"🎯 Limited to first {max_businesses} businesses for testing")
        else:
            print(f"🚀 Processing ALL {len(businesses_with_websites)} businesses with websites")
        
        # Track progress
        emails_found = 0
        processed = 0
        
        # Process each business with a website
        for index, row in businesses_with_websites.iterrows():
            processed += 1
            business_name = row['business_name']
            website_url = row['website_url']
            
            print(f"\n[{processed}/{len(businesses_with_websites)}] Processing: {business_name}")
            
            # Skip if email already exists
            if pd.notna(row['email_address']) and row['email_address'].strip():
                print(f"  ⏭️ Email already exists: {row['email_address']}")
                continue
            
            # Scrape for emails and social media
            emails, social_links = self.scrape_website_for_emails_and_social(website_url, business_name)

            if emails:
                # Use the first email found
                primary_email = emails[0]
                df.at[index, 'email_address'] = primary_email
                emails_found += 1
                print(f"  ✅ Added email: {primary_email}")

                # If multiple emails found, note them
                if len(emails) > 1:
                    print(f"    📝 Additional emails found: {', '.join(emails[1:])}")

            # Add social media links to separate columns
            if social_links:
                for platform, links in social_links.items():
                    if links:
                        column_name = f'{platform}_url'
                        # Add column if it doesn't exist
                        if column_name not in df.columns:
                            df[column_name] = ''
                        # Use the first link found for each platform
                        df.at[index, column_name] = links[0]
                        print(f"  📱 Added {platform}: {links[0]}")

                        # If multiple links found, note them
                        if len(links) > 1:
                            print(f"    📝 Additional {platform} links: {', '.join(links[1:])}")
            
            # Rate limiting - be respectful to websites
            time.sleep(1)
            
            # Progress update every 25 businesses
            if processed % 25 == 0:
                print(f"\n📈 Progress: {processed}/{len(businesses_with_websites)} processed, {emails_found} emails found")
                print(f"⏱️ Estimated time remaining: {((len(businesses_with_websites) - processed) * 3) // 60} minutes")
        
        # Save updated CSV
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = csv_file_path.replace('.csv', f'_with_emails_social_{timestamp}.csv')
        df.to_csv(output_file, index=False)

        print(f"\n🎉 EMAIL & SOCIAL MEDIA EXTRACTION COMPLETE!")
        print(f"📁 Updated file saved as: {output_file}")
        print(f"📧 Total emails found: {emails_found}")
        print(f"📊 Email coverage: {emails_found}/{len(businesses_with_websites)} ({emails_found/len(businesses_with_websites)*100:.1f}%)")

        # Summary statistics for emails
        total_with_emails = df['email_address'].notna().sum()
        print(f"📈 Total businesses with emails now: {total_with_emails}/{len(df)} ({total_with_emails/len(df)*100:.1f}%)")

        # Summary statistics for social media
        social_platforms = ['facebook', 'instagram', 'twitter', 'linkedin', 'youtube', 'tiktok', 'pinterest', 'snapchat', 'yelp', 'google_business']
        social_stats = {}

        for platform in social_platforms:
            column_name = f'{platform}_url'
            if column_name in df.columns:
                count = df[column_name].notna().sum()
                if count > 0:
                    social_stats[platform] = count

        if social_stats:
            print(f"\n📱 SOCIAL MEDIA LINKS FOUND:")
            for platform, count in social_stats.items():
                print(f"  {platform.title()}: {count} businesses")

        return output_file


def main():
    """Main function to run email scraping"""
    print("=" * 70)
    print("📧 COFFEE BUSINESS EMAIL SCRAPER")
    print("=" * 70)
    
    # Find the most recent independent coffee businesses CSV file
    output_dir = "output"
    csv_files = [f for f in os.listdir(output_dir) if f.startswith('pittsburgh_independent_coffee_businesses_') and f.endswith('.csv')]
    
    if not csv_files:
        print("❌ No independent coffee business CSV files found in output directory!")
        print("💡 Run filter_chains.py first to create the filtered dataset")
        return
    
    # Use the most recent file
    latest_file = max(csv_files, key=lambda x: os.path.getctime(os.path.join(output_dir, x)))
    csv_file_path = os.path.join(output_dir, latest_file)
    
    print(f"📁 Using file: {latest_file}")
    
    # Initialize scraper and process file (full dataset)
    scraper = EmailScraper()
    updated_file = scraper.process_csv_file(csv_file_path, max_businesses=None)  # None = process all
    
    print(f"\n✅ Email and social media scraping completed!")
    print(f"📁 Updated file: {updated_file}")


if __name__ == '__main__':
    main()
