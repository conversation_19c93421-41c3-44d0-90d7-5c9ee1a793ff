"""
Master Free Data Collection Script
Run all free data collection methods and combine results
"""
import os
import sys
import subprocess
from datetime import datetime


def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements_free.txt"])
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False


def run_openstreetmap_collection():
    """Run OpenStreetMap data collection"""
    print("\n🗺️  PHASE 1: OpenStreetMap Data Collection")
    print("=" * 50)
    
    try:
        from openstreetmap_collector import OpenStreetMapCollector
        
        collector = OpenStreetMapCollector()
        
        # Pittsburgh coordinates
        pittsburgh_lat = 40.4406
        pittsburgh_lng = -79.9959
        
        # Coffee-specific business types for OSM
        business_types = [
            'cafe', 'coffee_shop', 'restaurant'  # Focus on coffee-related amenities
        ]

        # Collect within 300-mile radius (same as previous dataset)
        print("Collecting independent coffee businesses within 300 miles...")
        businesses = collector.collect_businesses_in_radius(
            pittsburgh_lat, pittsburgh_lng, 300, business_types
        )
        
        if businesses:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"openstreetmap_independent_coffee_{timestamp}.csv"
            collector.save_to_csv(businesses, filename)
            print(f"✅ OpenStreetMap coffee collection complete: {filename}")
            return filename
        else:
            print("❌ No businesses collected from OpenStreetMap")
            return None
            
    except Exception as e:
        print(f"❌ Error in OpenStreetMap collection: {e}")
        return None


def run_email_enhancement(osm_file):
    """Run email enhancement on collected data"""
    print("\n📧 PHASE 2: Email Enhancement")
    print("=" * 50)
    
    if not osm_file or not os.path.exists(osm_file):
        print("❌ No OpenStreetMap file to enhance")
        return None
    
    try:
        from free_email_finder import FreeEmailFinder
        
        finder = FreeEmailFinder()
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"enhanced_with_emails_{timestamp}.csv"
        
        finder.enhance_business_data_with_emails(osm_file, output_file)
        
        if os.path.exists(output_file):
            print(f"✅ Email enhancement complete: {output_file}")
            return output_file
        else:
            print("❌ Email enhancement failed")
            return None
            
    except Exception as e:
        print(f"❌ Error in email enhancement: {e}")
        return None


def run_government_collection():
    """Run government data collection"""
    print("\n🏛️  PHASE 3: Government Data Collection")
    print("=" * 50)
    
    try:
        from government_data_collector import GovernmentDataCollector
        
        collector = GovernmentDataCollector()
        
        # Collect for Pittsburgh metro area
        locations = [
            "Pittsburgh, PA",
            "Philadelphia, PA", 
            "Columbus, OH",
            "Cleveland, OH",
            "Baltimore, MD"
        ]
        
        categories = [
            "coffee", "cafe", "coffee shop", "espresso", "roaster"
        ]
        
        all_businesses = []
        
        for location in locations:
            print(f"Collecting data for {location}...")
            businesses = collector.collect_all_government_data(location, categories)
            all_businesses.extend(businesses)
        
        if all_businesses:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"government_business_data_{timestamp}.csv"
            collector.save_to_csv(all_businesses, filename)
            print(f"✅ Government data collection complete: {filename}")
            return filename
        else:
            print("❌ No government data collected")
            return None
            
    except Exception as e:
        print(f"❌ Error in government data collection: {e}")
        return None


def run_master_combination():
    """Combine all collected data"""
    print("\n📊 PHASE 4: Master Data Combination")
    print("=" * 50)
    
    try:
        from master_data_combiner import MasterDataCombiner
        
        combiner = MasterDataCombiner()
        output_file = combiner.combine_all_data()
        
        if output_file:
            print(f"✅ Master combination complete: {output_file}")
            return output_file
        else:
            print("❌ Master combination failed")
            return None
            
    except Exception as e:
        print(f"❌ Error in master combination: {e}")
        return None


def main():
    """Run complete free data collection pipeline"""
    
    print("🚀 FREE BUSINESS DATA COLLECTION PIPELINE")
    print("=" * 60)
    print("This will collect business data from multiple FREE sources:")
    print("• OpenStreetMap (unlimited, free)")
    print("• Email discovery (pattern matching, WHOIS, web scraping)")
    print("• Government databases (public records)")
    print("• Yellow Pages & BBB (public listings)")
    print("=" * 60)
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements. Exiting.")
        return
    
    # Phase 1: OpenStreetMap Collection
    osm_file = run_openstreetmap_collection()
    
    # Phase 2: Email Enhancement
    enhanced_file = run_email_enhancement(osm_file)
    
    # Phase 3: Government Data Collection
    gov_file = run_government_collection()
    
    # Phase 4: Master Combination
    master_file = run_master_combination()
    
    # Final Summary
    print("\n🎉 COLLECTION PIPELINE COMPLETE!")
    print("=" * 60)
    
    files_created = []
    if osm_file:
        files_created.append(f"📍 OpenStreetMap data: {osm_file}")
    if enhanced_file:
        files_created.append(f"📧 Enhanced with emails: {enhanced_file}")
    if gov_file:
        files_created.append(f"🏛️  Government data: {gov_file}")
    if master_file:
        files_created.append(f"📊 MASTER FILE: {master_file}")
    
    if files_created:
        print("Files created:")
        for file_info in files_created:
            print(f"  {file_info}")
        
        if master_file:
            print(f"\n🎯 YOUR FINAL BUSINESS DIRECTORY: {master_file}")
            print("This file contains deduplicated business data from all free sources!")
            print("Ready for your marketing campaigns! 🚀")
    else:
        print("❌ No files were created. Check the error messages above.")
    
    print("\n💡 TIP: Run this script periodically to update your business directory")
    print("All data sources are completely FREE! 💰")


if __name__ == "__main__":
    main()
