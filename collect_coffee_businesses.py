#!/usr/bin/env python3
"""
Coffee Business Collection Script for Pittsburgh Area
Collects coffee shops, cafes, and coffee-related businesses within 300 miles of Pittsburgh
"""
import os
import time
import math
import csv
from datetime import datetime
import googlemaps
from config import settings


def calculate_distance(lat1, lng1, lat2, lng2):
    """Calculate distance between two points using Haversine formula"""
    R = 3959  # Earth's radius in miles
    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lng = math.radians(lng2 - lng1)
    
    a = (math.sin(delta_lat / 2) ** 2 + 
         math.cos(lat1_rad) * math.cos(lat2_rad) * 
         math.sin(delta_lng / 2) ** 2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    
    return R * c


def is_within_radius(lat, lng, center_lat=40.4406, center_lng=-79.9959, radius_miles=300):
    """Check if coordinates are within specified radius of center point"""
    distance = calculate_distance(center_lat, center_lng, lat, lng)
    return distance <= radius_miles, distance


def search_coffee_businesses(client, center_lat, center_lng, region_name, radius_km=80):
    """Search for coffee-related businesses in a specific region"""
    print(f"\n☕ Searching for coffee businesses in {region_name}...")
    
    coffee_businesses = []
    
    # Different search terms for coffee-related businesses
    search_terms = [
        'coffee shop',
        'cafe',
        'coffee house',
        'espresso bar',
        'coffee roaster',
        'coffee bean',
        'starbucks',
        'dunkin',
        'tim hortons'
    ]
    
    # Also search by type
    business_types = ['cafe', 'bakery']  # bakery often includes coffee shops
    
    # Search by text queries first
    for term in search_terms:
        try:
            print(f"  🔍 Searching for '{term}'...")
            
            # Use text search for better coffee-specific results
            result = client.places(
                query=f"{term} near {region_name}",
                location=(center_lat, center_lng),
                radius=radius_km * 1000,
                language='en'
            )
            
            if result['status'] == 'OK':
                places = result.get('results', [])
                print(f"    Found {len(places)} results for '{term}'")
                
                for place in places:
                    try:
                        place_id = place.get('place_id')
                        name = place.get('name', '')
                        location = place.get('geometry', {}).get('location', {})
                        lat = location.get('lat')
                        lng = location.get('lng')
                        
                        if not lat or not lng or not place_id:
                            continue
                            
                        # Check if within 300-mile radius of Pittsburgh
                        within_radius, distance = is_within_radius(lat, lng)
                        if not within_radius:
                            continue
                            
                        # Filter for coffee-related businesses by name
                        name_lower = name.lower()
                        coffee_keywords = [
                            'coffee', 'cafe', 'espresso', 'latte', 'cappuccino',
                            'roaster', 'bean', 'brew', 'starbucks', 'dunkin',
                            'tim hortons', 'peet', 'caribou', 'biggby'
                        ]
                        
                        if not any(keyword in name_lower for keyword in coffee_keywords):
                            continue
                            
                        # Get detailed information using valid field names
                        try:
                            details = client.place(
                                place_id=place_id,
                                fields=['name', 'formatted_address', 'formatted_phone_number',
                                       'website', 'rating', 'user_ratings_total', 'opening_hours',
                                       'price_level', 'business_status']
                            )
                            
                            if details['status'] == 'OK':
                                detail_info = details['result']
                                
                                # Extract address components
                                address = detail_info.get('formatted_address', '')
                                city = ''
                                state = ''
                                zip_code = ''
                                
                                if address:
                                    parts = address.split(', ')
                                    if len(parts) >= 3:
                                        city = parts[-3] if len(parts) > 2 else ''
                                        state_zip = parts[-2] if len(parts) > 1 else ''
                                        if ' ' in state_zip:
                                            state, zip_code = state_zip.split(' ', 1)
                                        else:
                                            state = state_zip
                                
                                # Extract business hours
                                hours = ''
                                if 'opening_hours' in detail_info:
                                    weekday_text = detail_info['opening_hours'].get('weekday_text', [])
                                    if weekday_text:
                                        hours = '; '.join(weekday_text[:3])  # First 3 days
                                
                                business_data = {
                                    'business_name': detail_info.get('name', name),
                                    'phone_number': detail_info.get('formatted_phone_number', ''),
                                    'email_address': '',  # Not available via API
                                    'website_url': detail_info.get('website', ''),
                                    'full_address': address,
                                    'city': city,
                                    'state': state,
                                    'zip_code': zip_code,
                                    'latitude': lat,
                                    'longitude': lng,
                                    'business_category': 'Coffee Shop/Cafe',
                                    'business_status': detail_info.get('business_status', ''),
                                    'google_rating': detail_info.get('rating', ''),
                                    'google_review_count': detail_info.get('user_ratings_total', ''),
                                    'price_level': detail_info.get('price_level', ''),
                                    'business_hours': hours,
                                    'distance_from_pittsburgh': round(distance, 1),
                                    'data_source': 'google_places',
                                    'search_term': term,
                                    'collection_region': region_name,
                                    'collected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                }
                                
                                coffee_businesses.append(business_data)
                                print(f"    ✅ {business_data['business_name']} - {city}, {state}")
                                
                        except Exception as e:
                            print(f"    ⚠️ Error getting details for {name}: {e}")
                            continue
                            
                        # Rate limiting
                        time.sleep(0.2)
                        
                    except Exception as e:
                        print(f"    ❌ Error processing place: {e}")
                        continue
                        
            # Rate limiting between searches
            time.sleep(1)
            
        except Exception as e:
            print(f"  ❌ Error searching for '{term}': {e}")
            continue
    
    # Also search by business type
    for business_type in business_types:
        try:
            print(f"  🔍 Searching by type: {business_type}...")
            
            result = client.places_nearby(
                location=(center_lat, center_lng),
                radius=radius_km * 1000,
                type=business_type,
                language='en'
            )
            
            if result['status'] == 'OK':
                places = result.get('results', [])
                print(f"    Found {len(places)} {business_type} businesses")
                
                for place in places:
                    try:
                        name = place.get('name', '')
                        name_lower = name.lower()
                        
                        # Only include if coffee-related
                        coffee_keywords = [
                            'coffee', 'cafe', 'espresso', 'latte', 'cappuccino',
                            'roaster', 'bean', 'brew', 'starbucks', 'dunkin'
                        ]
                        
                        if not any(keyword in name_lower for keyword in coffee_keywords):
                            continue
                            
                        place_id = place.get('place_id')
                        location = place.get('geometry', {}).get('location', {})
                        lat = location.get('lat')
                        lng = location.get('lng')
                        
                        if not lat or not lng or not place_id:
                            continue
                            
                        # Check if within radius
                        within_radius, distance = is_within_radius(lat, lng)
                        if not within_radius:
                            continue
                            
                        # Get details (simplified to avoid duplicate API calls)
                        business_data = {
                            'business_name': name,
                            'phone_number': '',
                            'email_address': '',
                            'website_url': '',
                            'full_address': place.get('vicinity', ''),
                            'city': '',
                            'state': '',
                            'zip_code': '',
                            'latitude': lat,
                            'longitude': lng,
                            'business_category': 'Coffee Shop/Cafe',
                            'business_status': '',
                            'google_rating': place.get('rating', ''),
                            'google_review_count': place.get('user_ratings_total', ''),
                            'price_level': place.get('price_level', ''),
                            'business_hours': '',
                            'distance_from_pittsburgh': round(distance, 1),
                            'data_source': 'google_places_nearby',
                            'search_term': business_type,
                            'collection_region': region_name,
                            'collected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
                        
                        coffee_businesses.append(business_data)
                        print(f"    ✅ {name}")
                        
                    except Exception as e:
                        print(f"    ❌ Error processing {business_type}: {e}")
                        continue
                        
            time.sleep(1)
            
        except Exception as e:
            print(f"  ❌ Error searching {business_type}: {e}")
            continue
    
    print(f"☕ Collected {len(coffee_businesses)} coffee businesses from {region_name}")
    return coffee_businesses


def main():
    """Main coffee business collection process"""
    print("=" * 70)
    print("☕ PITTSBURGH AREA COFFEE BUSINESS COLLECTION")
    print("=" * 70)
    print(f"📍 Target: 300-mile radius from Pittsburgh, PA")
    print(f"🎯 Focus: Coffee shops, cafes, and coffee-related businesses")
    print(f"🔑 Using Google Places API")
    print()
    
    # Initialize Google Maps client
    if not settings.GOOGLE_PLACES_API_KEY:
        print("❌ Google Places API key not found!")
        return
        
    client = googlemaps.Client(key=settings.GOOGLE_PLACES_API_KEY)
    
    # Define collection regions (major metropolitan areas within 300 miles)
    regions = [
        {"name": "Pittsburgh, PA", "lat": 40.4406, "lng": -79.9959, "radius": 50},
        {"name": "Philadelphia, PA", "lat": 39.9526, "lng": -75.1652, "radius": 50},
        {"name": "Cleveland, OH", "lat": 41.4993, "lng": -81.6944, "radius": 40},
        {"name": "Columbus, OH", "lat": 39.9612, "lng": -82.9988, "radius": 40},
        {"name": "Baltimore, MD", "lat": 39.2904, "lng": -76.6122, "radius": 40},
        {"name": "Washington, DC", "lat": 38.9072, "lng": -77.0369, "radius": 40},
        {"name": "Buffalo, NY", "lat": 42.8864, "lng": -78.8784, "radius": 30},
        {"name": "Charleston, WV", "lat": 38.3498, "lng": -81.6326, "radius": 30}
    ]
    
    all_coffee_businesses = []
    
    # Collect from each region
    for region in regions:
        try:
            businesses = search_coffee_businesses(
                client, 
                region["lat"], 
                region["lng"], 
                region["name"],
                region["radius"]
            )
            all_coffee_businesses.extend(businesses)
            
            print(f"📊 Total coffee businesses collected so far: {len(all_coffee_businesses)}")
            
            # Brief pause between regions
            time.sleep(3)
            
        except Exception as e:
            print(f"❌ Error collecting from {region['name']}: {e}")
            continue
    
    # Remove duplicates
    print(f"\n🔄 Removing duplicates...")
    unique_businesses = []
    seen = set()
    
    for business in all_coffee_businesses:
        # Create a key for deduplication
        key = (
            business['business_name'].lower().strip(),
            business['full_address'].lower().strip(),
            round(float(business['latitude']), 4) if business['latitude'] else 0,
            round(float(business['longitude']), 4) if business['longitude'] else 0
        )
        
        if key not in seen:
            seen.add(key)
            unique_businesses.append(business)
    
    print(f"✅ Removed {len(all_coffee_businesses) - len(unique_businesses)} duplicates")
    print(f"☕ Final count: {len(unique_businesses)} unique coffee businesses")
    
    # Export to CSV
    if unique_businesses:
        # Create output directory
        os.makedirs('output', exist_ok=True)
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"output/pittsburgh_coffee_businesses_{timestamp}.csv"
        
        # Write to CSV
        fieldnames = [
            'business_name', 'phone_number', 'email_address', 'website_url',
            'full_address', 'city', 'state', 'zip_code', 'latitude', 'longitude',
            'business_category', 'business_status', 'google_rating', 'google_review_count',
            'price_level', 'business_hours', 'distance_from_pittsburgh',
            'data_source', 'search_term', 'collection_region', 'collected_at'
        ]
        
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(unique_businesses)
        
        print(f"\n🎉 SUCCESS! Coffee business data exported to: {csv_filename}")
        
        # Generate summary
        with_phone = sum(1 for b in unique_businesses if b['phone_number'])
        with_website = sum(1 for b in unique_businesses if b['website_url'])
        with_rating = sum(1 for b in unique_businesses if b['google_rating'])
        avg_distance = sum(float(b['distance_from_pittsburgh']) for b in unique_businesses) / len(unique_businesses)
        
        if with_rating > 0:
            avg_rating = sum(float(b['google_rating']) for b in unique_businesses if b['google_rating']) / with_rating
        else:
            avg_rating = 0
        
        print(f"\n📈 COFFEE BUSINESS SUMMARY:")
        print(f"Total coffee businesses: {len(unique_businesses):,}")
        print(f"Businesses with phone: {with_phone:,} ({with_phone/len(unique_businesses)*100:.1f}%)")
        print(f"Businesses with website: {with_website:,} ({with_website/len(unique_businesses)*100:.1f}%)")
        print(f"Businesses with ratings: {with_rating:,} ({with_rating/len(unique_businesses)*100:.1f}%)")
        print(f"Average Google rating: {avg_rating:.1f}/5.0")
        print(f"Average distance from Pittsburgh: {avg_distance:.1f} miles")
        
        # Show breakdown by region
        from collections import Counter
        region_counts = Counter(b['collection_region'] for b in unique_businesses)
        print(f"\n📍 COFFEE BUSINESSES BY REGION:")
        for region, count in region_counts.most_common():
            print(f"  {region}: {count:,}")
            
        # Show top-rated coffee shops
        rated_businesses = [b for b in unique_businesses if b['google_rating']]
        if rated_businesses:
            top_rated = sorted(rated_businesses, key=lambda x: float(x['google_rating']), reverse=True)[:10]
            print(f"\n⭐ TOP-RATED COFFEE BUSINESSES:")
            for i, business in enumerate(top_rated, 1):
                print(f"  {i}. {business['business_name']} - {business['google_rating']}⭐ ({business['city']}, {business['state']})")
    
    else:
        print("❌ No coffee businesses collected!")
    
    print(f"\n✅ Coffee business collection complete!")


if __name__ == '__main__':
    main()
