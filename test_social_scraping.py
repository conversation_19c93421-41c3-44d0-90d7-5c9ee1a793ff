#!/usr/bin/env python3
"""
Test script to verify social media email extraction
"""
from email_scraper import <PERSON>ail<PERSON>craper

def test_social_scraping():
    """Test social media email extraction"""
    scraper = EmailScraper()
    
    # Test a few social media URLs
    test_cases = [
        {
            "url": "https://www.facebook.com/802BeanCompany/",
            "business": "802 Bean Company",
            "platform": "facebook"
        },
        {
            "url": "https://instagram.com/bellevuebeans_coffee",
            "business": "Bellevue Beans",
            "platform": "instagram"
        }
    ]
    
    print("🧪 Testing Social Media Email Extraction:")
    print("=" * 50)
    
    for test in test_cases:
        print(f"\n🔍 Testing: {test['business']}")
        print(f"URL: {test['url']}")
        print(f"Platform: {test['platform']}")
        print("-" * 30)
        
        try:
            emails = scraper.scrape_social_media_for_emails(
                test['url'], 
                test['platform'], 
                test['business']
            )
            
            if emails:
                print(f"✅ Emails found: {', '.join(emails)}")
            else:
                print("❌ No emails found")
                
        except Exception as e:
            print(f"⚠️ Error: {e}")

if __name__ == '__main__':
    test_social_scraping()
