"""
Efficient Coffee Business Collector with Social Media Scraping
Processes businesses in batches with progress saving
"""
import requests
import json
import csv
import time
from datetime import datetime
from typing import List, Dict, Optional
import math
import re
from urllib.parse import urlparse
from bs4 import BeautifulSoup
import os
import pandas as pd
from difflib import SequenceMatcher


class EfficientCoffeeCollector:
    """Efficient coffee business collector with social media scraping"""
    
    def __init__(self):
        self.base_url = "https://overpass-api.de/api/interpreter"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.timeout = 10
        
        # Chain coffee shops to exclude (same as your previous dataset)
        self.chains = [
            'starbucks', 'dunkin', 'dunkin donuts', 'tim hortons', 'tim horton',
            'mcdonalds', 'burger king', 'subway', 'kfc', 'taco bell',
            'pizza hut', 'dominos', 'papa johns', 'wendys', 'arbys',
            'dairy queen', 'sonic', 'panera', 'chipotle', 'qdoba',
            'five guys', 'jimmy johns', 'jersey mikes', 'firehouse subs',
            'einstein bros', 'caribou coffee', 'peets coffee', 'peet\'s coffee',
            'costa coffee', 'second cup', 'gloria jeans', 'green mountain coffee',
            'folgers', 'maxwell house', 'nescafe', 'lavazza'
        ]
        
        # Social media patterns
        self.social_patterns = {
            'facebook': r'(?:https?://)?(?:www\.)?facebook\.com/[^/\s]+',
            'instagram': r'(?:https?://)?(?:www\.)?instagram\.com/[^/\s]+',
            'twitter': r'(?:https?://)?(?:www\.)?(?:twitter|x)\.com/[^/\s]+',
            'linkedin': r'(?:https?://)?(?:www\.)?linkedin\.com/(?:company|in)/[^/\s]+',
            'youtube': r'(?:https?://)?(?:www\.)?youtube\.com/(?:channel|c|user)/[^/\s]+',
            'tiktok': r'(?:https?://)?(?:www\.)?tiktok\.com/@[^/\s]+',
            'yelp': r'(?:https?://)?(?:www\.)?yelp\.com/biz/[^/\s]+'
        }
        
        self.email_regex = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
    
    def get_coffee_businesses_osm(self, lat: float, lng: float, radius_miles: float) -> List[Dict]:
        """Get coffee businesses from OpenStreetMap"""
        radius_m = int(radius_miles * 1609.34)  # Convert miles to meters
        
        query = f"""[out:json][timeout:180];
(
  node["amenity"="cafe"](around:{radius_m},{lat},{lng});
  way["amenity"="cafe"](around:{radius_m},{lat},{lng});
  node["shop"="coffee"](around:{radius_m},{lat},{lng});
  way["shop"="coffee"](around:{radius_m},{lat},{lng});
);
out center meta;"""
        
        try:
            response = self.session.post(self.base_url, data={'data': query}, timeout=180)
            response.raise_for_status()
            data = response.json()
            
            businesses = []
            elements = data.get('elements', [])
            
            print(f"Processing {len(elements)} raw businesses...")

            for i, element in enumerate(elements):
                if i % 500 == 0:
                    print(f"  Processed {i}/{len(elements)} businesses...")

                business = self.extract_basic_business_data(element, lat, lng)
                if business and self.is_independent_coffee(business['business_name']):
                    businesses.append(business)
            
            return businesses
            
        except Exception as e:
            print(f"Error querying OpenStreetMap: {e}")
            return []
    
    def extract_basic_business_data(self, element: Dict, center_lat: float, center_lng: float) -> Optional[Dict]:
        """Extract basic business data from OSM element"""
        tags = element.get('tags', {})
        
        if 'name' not in tags:
            return None
        
        # Get coordinates
        if element['type'] == 'node':
            lat, lng = element['lat'], element['lon']
        elif 'center' in element:
            lat, lng = element['center']['lat'], element['center']['lon']
        else:
            return None
        
        # Calculate distance
        distance = self.calculate_distance(center_lat, center_lng, lat, lng)
        
        return {
            'business_name': tags.get('name', ''),
            'phone_number': tags.get('phone', ''),
            'email_address': tags.get('email', ''),
            'website_url': tags.get('website', ''),
            'full_address': self.build_address(tags),
            'city': tags.get('addr:city', ''),
            'state': tags.get('addr:state', ''),
            'zip_code': tags.get('addr:postcode', ''),
            'latitude': lat,
            'longitude': lng,
            'business_category': 'Independent Coffee Shop/Cafe',
            'business_hours': tags.get('opening_hours', ''),
            'distance_from_pittsburgh': round(distance, 1),
            'facebook_url': '',
            'instagram_url': '',
            'twitter_url': '',
            'linkedin_url': '',
            'youtube_url': '',
            'tiktok_url': '',
            'pinterest_url': '',
            'yelp_url': '',
            'google_business_url': '',
            'data_source': 'openstreetmap',
            'osm_id': element['id'],
            'osm_type': element['type'],
            'collected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """Calculate distance in miles"""
        R = 3959
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lng = math.radians(lng2 - lng1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lng / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def build_address(self, tags: Dict) -> str:
        """Build address from OSM tags"""
        parts = []
        if 'addr:housenumber' in tags:
            parts.append(tags['addr:housenumber'])
        if 'addr:street' in tags:
            parts.append(tags['addr:street'])
        return ' '.join(parts)
    
    def is_independent_coffee(self, name: str) -> bool:
        """Check if coffee business is independent (not a chain)"""
        if not name:
            return False

        name_lower = name.lower().strip()

        # First check if it's a chain (more strict filtering)
        for chain in self.chains:
            if chain in name_lower:
                print(f"    ❌ Filtered out chain: {name}")
                return False

        # Check if it's coffee-related
        coffee_keywords = ['coffee', 'cafe', 'espresso', 'roaster', 'bean', 'brew', 'latte', 'cappuccino']
        is_coffee = any(keyword in name_lower for keyword in coffee_keywords)

        if is_coffee:
            print(f"    ✅ Independent coffee: {name}")
            return True
        else:
            return False
    
    def scrape_social_media(self, url: str, business_name: str) -> Dict:
        """Scrape website for social media links and emails"""
        result = {
            'emails': [],
            'social_links': {platform: [] for platform in self.social_patterns.keys()}
        }
        
        if not url:
            return result
        
        try:
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            text = soup.get_text()
            
            # Extract emails
            emails = self.email_regex.findall(text)
            business_emails = [email for email in emails if not any(
                domain in email.lower() for domain in ['gmail.com', 'yahoo.com', 'hotmail.com']
            )]
            result['emails'] = business_emails[:3]  # Limit to 3 emails
            
            # Extract social media links from page content
            page_content = response.text

            for platform, pattern in self.social_patterns.items():
                matches = re.findall(pattern, page_content, re.IGNORECASE)
                for match in matches:
                    if not match.startswith(('http://', 'https://')):
                        match = 'https://' + match
                    if match not in result['social_links'][platform]:
                        result['social_links'][platform].append(match)
            
        except Exception as e:
            print(f"    ❌ Error scraping {business_name}: {e}")
        
        return result
    
    def enhance_with_social_media(self, businesses: List[Dict], max_to_process: Optional[int] = None) -> List[Dict]:
        """Enhance businesses with social media data"""
        if max_to_process:
            businesses = businesses[:max_to_process]
        
        enhanced = []
        
        for i, business in enumerate(businesses):
            print(f"Processing {i+1}/{len(businesses)}: {business['business_name']}")
            
            # Scrape website if available
            if business['website_url']:
                social_data = self.scrape_social_media(business['website_url'], business['business_name'])
                
                # Update email if found and not already present
                if social_data['emails'] and not business['email_address']:
                    business['email_address'] = social_data['emails'][0]
                
                # Update social media links
                for platform, links in social_data['social_links'].items():
                    if links:
                        business[f'{platform}_url'] = links[0]
            
            enhanced.append(business)
            time.sleep(1)  # Be respectful
        
        return enhanced

    def normalize_business_name(self, name: str) -> str:
        """Normalize business name for comparison"""
        if not name:
            return ""

        # Convert to lowercase and remove common suffixes/prefixes
        name = str(name).lower().strip()

        # Remove common business suffixes
        suffixes = ['llc', 'inc', 'corp', 'ltd', 'co', 'company', 'restaurant', 'cafe', 'shop', 'coffee']
        words = name.split()
        filtered_words = [w for w in words if w not in suffixes]

        return ' '.join(filtered_words) if filtered_words else name

    def calculate_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity between two business names"""
        norm1 = self.normalize_business_name(name1)
        norm2 = self.normalize_business_name(name2)
        return SequenceMatcher(None, norm1, norm2).ratio()

    def are_duplicates(self, business1: Dict, business2: Dict) -> bool:
        """Check if two businesses are duplicates"""
        # Check name similarity
        name_similarity = self.calculate_similarity(business1['business_name'], business2['business_name'])

        # If names are very similar (>80% match)
        if name_similarity > 0.8:
            # Check if they're in the same location (within 0.1 miles)
            if (business1.get('latitude') and business1.get('longitude') and
                business2.get('latitude') and business2.get('longitude')):

                distance = self.calculate_distance(
                    business1['latitude'], business1['longitude'],
                    business2['latitude'], business2['longitude']
                )

                if distance < 0.1:  # Within 0.1 miles
                    return True

        # Check if they have the same exact address
        if (business1.get('full_address') and business2.get('full_address') and
            business1['full_address'].strip().lower() == business2['full_address'].strip().lower()):
            return True

        return False

    def select_best_business(self, duplicates: List[Dict]) -> Dict:
        """Select the best business record from duplicates"""
        def score_business(business):
            score = 0

            # Points for having data
            if business.get('email_address'):
                score += 10
            if business.get('phone_number'):
                score += 8
            if business.get('website_url'):
                score += 6
            if business.get('full_address'):
                score += 4
            if business.get('latitude') and business.get('longitude'):
                score += 5

            # Points for social media
            social_fields = ['facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url']
            for field in social_fields:
                if business.get(field):
                    score += 2

            return score

        # Return the business with the highest score
        return max(duplicates, key=score_business)

    def remove_duplicates(self, businesses: List[Dict]) -> List[Dict]:
        """Remove duplicate businesses from the list"""
        print(f"\n🔍 Removing duplicates from {len(businesses)} businesses...")

        unique_businesses = []
        processed_indices = set()

        for i, business in enumerate(businesses):
            if i in processed_indices:
                continue

            # Find all duplicates of this business
            duplicates = [business]
            duplicate_indices = {i}

            for j, other_business in enumerate(businesses[i+1:], i+1):
                if j in processed_indices:
                    continue

                if self.are_duplicates(business, other_business):
                    duplicates.append(other_business)
                    duplicate_indices.add(j)

            # Mark all duplicates as processed
            processed_indices.update(duplicate_indices)

            # Select the best business from duplicates
            best_business = self.select_best_business(duplicates)
            unique_businesses.append(best_business)

            if len(duplicates) > 1:
                print(f"  📍 Found {len(duplicates)} duplicates for '{business['business_name']}', kept best one")

        print(f"✅ Removed {len(businesses) - len(unique_businesses)} duplicates")
        print(f"📊 Final count: {len(unique_businesses)} unique businesses")

        return unique_businesses
    
    def save_to_csv(self, businesses: List[Dict], filename: str):
        """Save businesses to CSV"""
        if not businesses:
            return
        
        fieldnames = [
            'business_name', 'phone_number', 'email_address', 'website_url',
            'full_address', 'city', 'state', 'zip_code', 'latitude', 'longitude',
            'business_category', 'business_hours', 'distance_from_pittsburgh',
            'facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url',
            'youtube_url', 'tiktok_url', 'pinterest_url', 'yelp_url', 'google_business_url',
            'data_source', 'osm_id', 'osm_type', 'collected_at'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(businesses)
        
        print(f"✅ Saved {len(businesses)} businesses to {filename}")


def main():
    """Main execution - collect ALL businesses with deduplication"""
    collector = EfficientCoffeeCollector()

    # Pittsburgh coordinates
    pittsburgh_lat = 40.4406
    pittsburgh_lng = -79.9959

    print("☕ COMPLETE INDEPENDENT COFFEE BUSINESS COLLECTOR")
    print("=" * 60)
    print("🎯 Collecting ALL independent coffee businesses within 300 miles")
    print("📱 Including social media scraping and email discovery")
    print("🔍 With automatic duplicate removal")
    print("=" * 60)

    # Get businesses from OpenStreetMap
    print("\n🗺️  Step 1: Collecting coffee businesses from OpenStreetMap...")
    businesses = collector.get_coffee_businesses_osm(pittsburgh_lat, pittsburgh_lng, 300)

    print(f"📊 Found {len(businesses)} independent coffee businesses")

    if not businesses:
        print("❌ No businesses found")
        return

    # Process ALL businesses with social media scraping
    print(f"\n📱 Step 2: Enhancing ALL businesses with social media data...")
    print(f"⏱️  Estimated time: ~{len(businesses)*1.5/60:.0f} minutes")
    print("🔄 Processing in progress...")

    enhanced_businesses = collector.enhance_with_social_media(businesses, None)  # Process all

    # Remove duplicates
    print(f"\n� Step 3: Removing duplicates...")
    unique_businesses = collector.remove_duplicates(enhanced_businesses)

    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"complete_independent_coffee_businesses_{timestamp}.csv"
    collector.save_to_csv(unique_businesses, filename)

    # Print comprehensive summary
    with_emails = sum(1 for b in unique_businesses if b.get('email_address'))
    with_phone = sum(1 for b in unique_businesses if b.get('phone_number'))
    with_website = sum(1 for b in unique_businesses if b.get('website_url'))
    with_facebook = sum(1 for b in unique_businesses if b.get('facebook_url'))
    with_instagram = sum(1 for b in unique_businesses if b.get('instagram_url'))
    with_twitter = sum(1 for b in unique_businesses if b.get('twitter_url'))
    with_linkedin = sum(1 for b in unique_businesses if b.get('linkedin_url'))

    print(f"\n🎉 COMPLETE COLLECTION SUMMARY:")
    print("=" * 50)
    print(f"📊 Total unique businesses: {len(unique_businesses)}")
    print(f"📧 With emails: {with_emails} ({with_emails/len(unique_businesses)*100:.1f}%)")
    print(f"📞 With phone numbers: {with_phone} ({with_phone/len(unique_businesses)*100:.1f}%)")
    print(f"🌐 With websites: {with_website} ({with_website/len(unique_businesses)*100:.1f}%)")
    print(f"📱 SOCIAL MEDIA COVERAGE:")
    print(f"   📘 Facebook: {with_facebook}")
    print(f"   📸 Instagram: {with_instagram}")
    print(f"   🐦 Twitter: {with_twitter}")
    print(f"   💼 LinkedIn: {with_linkedin}")
    print("=" * 50)
    print(f"🎯 FINAL DEDUPLICATED FILE: {filename}")
    print("✅ Ready for your marketing campaigns!")

    # Compare with your previous dataset
    print(f"\n📈 COMPARISON TO PREVIOUS DATASET:")
    print(f"   Previous: 327 coffee businesses")
    print(f"   New: {len(unique_businesses)} coffee businesses")
    print(f"   Increase: {len(unique_businesses) - 327} businesses ({((len(unique_businesses) - 327) / 327 * 100):.0f}% more!)")

    if with_emails > 107:
        print(f"   Previous emails: 107 (32.7%)")
        print(f"   New emails: {with_emails} ({with_emails/len(unique_businesses)*100:.1f}%)")
        print(f"   Email improvement: {with_emails - 107} more emails!")


if __name__ == "__main__":
    main()
