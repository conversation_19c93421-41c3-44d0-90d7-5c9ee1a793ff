"""
Efficient Coffee Business Collector with Social Media Scraping
Processes businesses in batches with progress saving
"""
import requests
import json
import csv
import time
from datetime import datetime
from typing import List, Dict, Optional
import math
import re
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup
import os


class EfficientCoffeeCollector:
    """Efficient coffee business collector with social media scraping"""
    
    def __init__(self):
        self.base_url = "https://overpass-api.de/api/interpreter"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.timeout = 10
        
        # Chain coffee shops to exclude
        self.chains = [
            'starbucks', 'dunkin', 'tim hortons', 'tim horton',
            'mcdonalds', 'burger king', 'subway', 'kfc',
            'panera', 'chipotle', 'caribou coffee', 'peets coffee',
            'costa coffee', 'second cup', 'gloria jeans'
        ]
        
        # Social media patterns
        self.social_patterns = {
            'facebook': r'(?:https?://)?(?:www\.)?facebook\.com/[^/\s]+',
            'instagram': r'(?:https?://)?(?:www\.)?instagram\.com/[^/\s]+',
            'twitter': r'(?:https?://)?(?:www\.)?(?:twitter|x)\.com/[^/\s]+',
            'linkedin': r'(?:https?://)?(?:www\.)?linkedin\.com/(?:company|in)/[^/\s]+',
            'youtube': r'(?:https?://)?(?:www\.)?youtube\.com/(?:channel|c|user)/[^/\s]+',
            'tiktok': r'(?:https?://)?(?:www\.)?tiktok\.com/@[^/\s]+',
            'yelp': r'(?:https?://)?(?:www\.)?yelp\.com/biz/[^/\s]+'
        }
        
        self.email_regex = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
    
    def get_coffee_businesses_osm(self, lat: float, lng: float, radius_miles: float) -> List[Dict]:
        """Get coffee businesses from OpenStreetMap"""
        radius_m = int(radius_miles * 1609.34)  # Convert miles to meters
        
        query = f"""[out:json][timeout:180];
(
  node["amenity"="cafe"](around:{radius_m},{lat},{lng});
  way["amenity"="cafe"](around:{radius_m},{lat},{lng});
  node["shop"="coffee"](around:{radius_m},{lat},{lng});
  way["shop"="coffee"](around:{radius_m},{lat},{lng});
);
out center meta;"""
        
        try:
            response = self.session.post(self.base_url, data={'data': query}, timeout=180)
            response.raise_for_status()
            data = response.json()
            
            businesses = []
            elements = data.get('elements', [])
            
            for element in elements:
                business = self.extract_basic_business_data(element, lat, lng)
                if business and self.is_independent_coffee(business['business_name']):
                    businesses.append(business)
            
            return businesses
            
        except Exception as e:
            print(f"Error querying OpenStreetMap: {e}")
            return []
    
    def extract_basic_business_data(self, element: Dict, center_lat: float, center_lng: float) -> Optional[Dict]:
        """Extract basic business data from OSM element"""
        tags = element.get('tags', {})
        
        if 'name' not in tags:
            return None
        
        # Get coordinates
        if element['type'] == 'node':
            lat, lng = element['lat'], element['lon']
        elif 'center' in element:
            lat, lng = element['center']['lat'], element['center']['lon']
        else:
            return None
        
        # Calculate distance
        distance = self.calculate_distance(center_lat, center_lng, lat, lng)
        
        return {
            'business_name': tags.get('name', ''),
            'phone_number': tags.get('phone', ''),
            'email_address': tags.get('email', ''),
            'website_url': tags.get('website', ''),
            'full_address': self.build_address(tags),
            'city': tags.get('addr:city', ''),
            'state': tags.get('addr:state', ''),
            'zip_code': tags.get('addr:postcode', ''),
            'latitude': lat,
            'longitude': lng,
            'business_category': 'Independent Coffee Shop/Cafe',
            'business_hours': tags.get('opening_hours', ''),
            'distance_from_pittsburgh': round(distance, 1),
            'facebook_url': '',
            'instagram_url': '',
            'twitter_url': '',
            'linkedin_url': '',
            'youtube_url': '',
            'tiktok_url': '',
            'pinterest_url': '',
            'yelp_url': '',
            'google_business_url': '',
            'data_source': 'openstreetmap',
            'osm_id': element['id'],
            'osm_type': element['type'],
            'collected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """Calculate distance in miles"""
        R = 3959
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lng = math.radians(lng2 - lng1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lng / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def build_address(self, tags: Dict) -> str:
        """Build address from OSM tags"""
        parts = []
        if 'addr:housenumber' in tags:
            parts.append(tags['addr:housenumber'])
        if 'addr:street' in tags:
            parts.append(tags['addr:street'])
        return ' '.join(parts)
    
    def is_independent_coffee(self, name: str) -> bool:
        """Check if coffee business is independent (not a chain)"""
        if not name:
            return False
        
        name_lower = name.lower()
        
        # Check if it's coffee-related
        coffee_keywords = ['coffee', 'cafe', 'espresso', 'roaster', 'bean', 'brew', 'latte']
        is_coffee = any(keyword in name_lower for keyword in coffee_keywords)
        
        # Check if it's a chain
        is_chain = any(chain in name_lower for chain in self.chains)
        
        return is_coffee and not is_chain
    
    def scrape_social_media(self, url: str, business_name: str) -> Dict:
        """Scrape website for social media links and emails"""
        result = {
            'emails': [],
            'social_links': {platform: [] for platform in self.social_patterns.keys()}
        }
        
        if not url:
            return result
        
        try:
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            text = soup.get_text()
            
            # Extract emails
            emails = self.email_regex.findall(text)
            business_emails = [email for email in emails if not any(
                domain in email.lower() for domain in ['gmail.com', 'yahoo.com', 'hotmail.com']
            )]
            result['emails'] = business_emails[:3]  # Limit to 3 emails
            
            # Extract social media links
            all_links = soup.find_all('a', href=True)
            for link in all_links:
                href = link.get('href', '')
                full_url = urljoin(url, href)
                
                for platform, pattern in self.social_patterns.items():
                    if re.search(pattern, full_url, re.IGNORECASE):
                        if full_url not in result['social_links'][platform]:
                            result['social_links'][platform].append(full_url)
                        break
            
        except Exception as e:
            print(f"    ❌ Error scraping {business_name}: {e}")
        
        return result
    
    def enhance_with_social_media(self, businesses: List[Dict], max_to_process: int = None) -> List[Dict]:
        """Enhance businesses with social media data"""
        if max_to_process:
            businesses = businesses[:max_to_process]
        
        enhanced = []
        
        for i, business in enumerate(businesses):
            print(f"Processing {i+1}/{len(businesses)}: {business['business_name']}")
            
            # Scrape website if available
            if business['website_url']:
                social_data = self.scrape_social_media(business['website_url'], business['business_name'])
                
                # Update email if found and not already present
                if social_data['emails'] and not business['email_address']:
                    business['email_address'] = social_data['emails'][0]
                
                # Update social media links
                for platform, links in social_data['social_links'].items():
                    if links:
                        business[f'{platform}_url'] = links[0]
            
            enhanced.append(business)
            time.sleep(1)  # Be respectful
        
        return enhanced
    
    def save_to_csv(self, businesses: List[Dict], filename: str):
        """Save businesses to CSV"""
        if not businesses:
            return
        
        fieldnames = [
            'business_name', 'phone_number', 'email_address', 'website_url',
            'full_address', 'city', 'state', 'zip_code', 'latitude', 'longitude',
            'business_category', 'business_hours', 'distance_from_pittsburgh',
            'facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url',
            'youtube_url', 'tiktok_url', 'pinterest_url', 'yelp_url', 'google_business_url',
            'data_source', 'osm_id', 'osm_type', 'collected_at'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(businesses)
        
        print(f"✅ Saved {len(businesses)} businesses to {filename}")


def main():
    """Main execution with user control"""
    collector = EfficientCoffeeCollector()
    
    # Pittsburgh coordinates
    pittsburgh_lat = 40.4406
    pittsburgh_lng = -79.9959
    
    print("☕ INDEPENDENT COFFEE BUSINESS COLLECTOR WITH SOCIAL MEDIA")
    print("=" * 60)
    
    # Get businesses from OpenStreetMap
    print("🗺️  Step 1: Collecting coffee businesses from OpenStreetMap...")
    businesses = collector.get_coffee_businesses_osm(pittsburgh_lat, pittsburgh_lng, 300)
    
    print(f"📊 Found {len(businesses)} independent coffee businesses")
    
    if not businesses:
        print("❌ No businesses found")
        return
    
    # Ask user how many to process with social media scraping
    print(f"\n🤔 Social media scraping takes time (~2 seconds per business)")
    print(f"   Processing all {len(businesses)} would take ~{len(businesses)*2/60:.0f} minutes")
    
    try:
        max_process = input(f"\nHow many businesses to process with social media? (Enter number or 'all'): ").strip()
        
        if max_process.lower() == 'all':
            max_to_process = None
        else:
            max_to_process = int(max_process)
    except:
        max_to_process = 100  # Default to 100
        print(f"Using default: {max_to_process} businesses")
    
    # Enhance with social media
    print(f"\n📱 Step 2: Enhancing with social media data...")
    enhanced_businesses = collector.enhance_with_social_media(businesses, max_to_process)
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"independent_coffee_with_social_{timestamp}.csv"
    collector.save_to_csv(enhanced_businesses, filename)
    
    # Print summary
    with_emails = sum(1 for b in enhanced_businesses if b['email_address'])
    with_facebook = sum(1 for b in enhanced_businesses if b['facebook_url'])
    with_instagram = sum(1 for b in enhanced_businesses if b['instagram_url'])
    
    print(f"\n📈 SUMMARY:")
    print(f"   Total businesses: {len(enhanced_businesses)}")
    print(f"   With emails: {with_emails} ({with_emails/len(enhanced_businesses)*100:.1f}%)")
    print(f"   With Facebook: {with_facebook}")
    print(f"   With Instagram: {with_instagram}")
    print(f"\n🎯 Final file: {filename}")


if __name__ == "__main__":
    main()
