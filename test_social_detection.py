#!/usr/bin/env python3
"""
Test script to verify social media detection and email extraction
"""
from email_scraper import <PERSON><PERSON><PERSON><PERSON>raper

def test_social_detection():
    """Test social media platform detection"""
    scraper = EmailScraper()
    
    test_urls = [
        "https://www.facebook.com/802BeanCompany/",
        "https://instagram.com/bellevuebeans_coffee?igshid=MzRlODBiNWFlZA==",
        "https://www.instagram.com/mrrabbitphilly",
        "https://www.facebook.com/PhillyCafeOle/",
        "https://www.instagram.com/drinkblackturtle/",
        "http://facebook.com/commongroundscoffee",
        "https://www.facebook.com/GypsyBeans/",
        "https://www.instagram.com/twotigerscoffee",
        "https://instagram.com/olivia.coffee.co?igshid=MWI4MTIyMDE=",
        "https://www.instagram.com/childhoodcafedc",
        "https://m.facebook.com/pages/Lot-38-Espresso-Bar/236209893248851",
        "https://www.facebook.com/DailyDoseWV/",
        "https://m.facebook.com/appalachiancoffeehouse",
        "https://m.facebook.com/pages/The-Old-Village-Roaster/141593935880990",
        "https://www.facebook.com/TheCoffeeBeanWeirton"
    ]
    
    print("🧪 Testing Social Media Detection:")
    print("=" * 50)
    
    for url in test_urls:
        platform = scraper.detect_social_media_platform(url)
        print(f"URL: {url}")
        print(f"Detected Platform: {platform}")
        print("-" * 30)
        
        if platform:
            print(f"✅ Would scrape as {platform} page")
            # Test actual scraping (commented out to avoid hitting rate limits)
            # emails = scraper.scrape_social_media_for_emails(url, platform, "Test Business")
            # print(f"Emails found: {emails}")
        else:
            print("❌ Not detected as social media")
        
        print()

if __name__ == '__main__':
    test_social_detection()
