Metadata-Version: 2.4
Name: googlemaps
Version: 4.10.0
Summary: Python client library for Google Maps Platform
Home-page: https://github.com/googlemaps/google-maps-services-python
License: Apache 2.0
Platform: Posix; MacOS X; Windows
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Topic :: Internet
Requires-Python: >=3.5
Description-Content-Type: text/markdown
License-File: LICENSE
License-File: AUTHORS
Requires-Dist: requests<3.0,>=2.20.0
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: platform
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

Python Client for Google Maps Services
====================================

![Test](https://github.com/googlemaps/google-maps-services-js/workflows/Test/badge.svg)
![Release](https://github.com/googlemaps/google-maps-services-js/workflows/Release/badge.svg)
[![codecov](https://codecov.io/gh/googlemaps/google-maps-services-python/branch/master/graph/badge.svg)](https://codecov.io/gh/googlemaps/google-maps-services-python)
[![PyPI version](https://badge.fury.io/py/googlemaps.svg)](https://badge.fury.io/py/googlemaps)
![PyPI - Downloads](https://img.shields.io/pypi/dd/googlemaps)
![GitHub contributors](https://img.shields.io/github/contributors/googlemaps/google-maps-services-python)

## Description

Use Python? Want to geocode something? Looking for directions?
Maybe matrices of directions? This library brings the Google Maps Platform Web
Services to your Python application.

The Python Client for Google Maps Services is a Python Client library for the following Google Maps
APIs:

 - Directions API
 - Distance Matrix API
 - Elevation API
 - Geocoding API
 - Geolocation API
 - Time Zone API
 - Roads API
 - Places API
 - Maps Static API
 - Address Validation API

Keep in mind that the same [terms and conditions](https://developers.google.com/maps/terms) apply
to usage of the APIs when they're accessed through this library.

## Support

This library is community supported. We're comfortable enough with the stability and features of
the library that we want you to build real production applications on it. We will try to support,
through Stack Overflow, the public and protected surface of the library and maintain backwards
compatibility in the future; however, while the library is in version 0.x, we reserve the right
to make backwards-incompatible changes. If we do remove some functionality (typically because
better functionality exists or if the feature proved infeasible), our intention is to deprecate
and give developers a year to update their code.

If you find a bug, or have a feature suggestion, please log an issue. If you'd like to
contribute, please read contribute.

## Requirements

 - Python 3.5 or later.
 - A Google Maps API key.

## API Keys

Each Google Maps Web Service request requires an API key or client ID. API keys
are generated in the 'Credentials' page of the 'APIs & Services' tab of [Google Cloud console](https://console.cloud.google.com/apis/credentials).

For even more information on getting started with Google Maps Platform and generating/restricting an API key, see [Get Started with Google Maps Platform](https://developers.google.com/maps/gmp-get-started) in our docs.

**Important:** This key should be kept secret on your server.

## Installation

    $ pip install -U googlemaps

Note that you will need requests 2.4.0 or higher if you want to specify connect/read timeouts.

## Usage

This example uses the Geocoding API and the Directions API with an API key:

```python
import googlemaps
from datetime import datetime

gmaps = googlemaps.Client(key='Add Your Key here')

# Geocoding an address
geocode_result = gmaps.geocode('1600 Amphitheatre Parkway, Mountain View, CA')

# Look up an address with reverse geocoding
reverse_geocode_result = gmaps.reverse_geocode((40.714224, -73.961452))

# Request directions via public transit
now = datetime.now()
directions_result = gmaps.directions("Sydney Town Hall",
                                     "Parramatta, NSW",
                                     mode="transit",
                                     departure_time=now)

# Validate an address with address validation
addressvalidation_result =  gmaps.addressvalidation(['1600 Amphitheatre Pk'], 
                                                    regionCode='US',
                                                    locality='Mountain View', 
                                                    enableUspsCass=True)
```

For more usage examples, check out [the tests](https://github.com/googlemaps/google-maps-services-python/tree/master/tests).

## Features

### Retry on Failure

Automatically retry when intermittent failures occur. That is, when any of the retriable 5xx errors
are returned from the API.


## Building the Project


    # Installing nox
    $ pip install nox

    # Running tests
    $ nox

    # Generating documentation
    $ nox -e docs

    # Copy docs to gh-pages
    $ nox -e docs && mv docs/_build/html generated_docs && git clean -Xdi && git checkout gh-pages

## Documentation & resources

[Documentation for the `google-maps-services-python` library](https://googlemaps.github.io/google-maps-services-python/docs/index.html)

### Getting started
- [Get Started with Google Maps Platform](https://developers.google.com/maps/gmp-get-started)
- [Generating/restricting an API key](https://developers.google.com/maps/gmp-get-started#api-key)
- [Authenticating with a client ID](https://developers.google.com/maps/documentation/directions/get-api-key#client-id)

### API docs
- [Google Maps Platform web services](https://developers.google.com/maps/apis-by-platform#web_service_apis)
- [Directions API](https://developers.google.com/maps/documentation/directions/)
- [Distance Matrix API](https://developers.google.com/maps/documentation/distancematrix/)
- [Elevation API](https://developers.google.com/maps/documentation/elevation/)
- [Geocoding API](https://developers.google.com/maps/documentation/geocoding/)
- [Geolocation API](https://developers.google.com/maps/documentation/geolocation/)
- [Time Zone API](https://developers.google.com/maps/documentation/timezone/)
- [Roads API](https://developers.google.com/maps/documentation/roads/)
- [Places API](https://developers.google.com/places/)
- [Maps Static API](https://developers.google.com/maps/documentation/maps-static/)

### Support
- [Report an issue](https://github.com/googlemaps/google-maps-services-python/issues)
- [Contribute](https://github.com/googlemaps/google-maps-services-python/blob/master/CONTRIB.md)
- [StackOverflow](http://stackoverflow.com/questions/tagged/google-maps)
# Changelog
All notable changes to this project will be documented in this file.

## [v4.2.0]
### Added
- Add support for Maps Static API (#344)

## [v4.1.0]
### Added
- Adding support for passing in `experience_id` to Client class (#338)

## [v4.0.0]
### Changed
- Python 2 is no longer supported
- Removed place fields: `alt_id`, `id`, `reference`, and `scope`. Read more about this at https://developers.google.com/maps/deprecations.

## [v3.1.4]
### Changed
- `APIError.__str__` should always return a str (#328)

## [v3.1.3]
### Changed
- deprecation warning for place fields: `alt_id`, `id`, `reference`, and `scope`. Read more about this at https://developers.google.com/maps/deprecations.

## [v3.1.2]
### Added
- Tests for distribution tar as part of CI
- Support for subfields such as `geometry/location` and `geometry/viewport` in Places.

## [v3.1.1]
### Changed
- Added changelog to manifest

## [v3.1.0]
### Changed
- Switched build system to use [nox](https://nox.thea.codes/en/stable/), pytest, and codecov. Added Python 3.7 to test framework.
- Set precision of truncated latitude and longitude floats [to 8 decimals](https://github.com/googlemaps/google-maps-services-python/pull/301) instead of 6.
- Minimum version of requests increased.
- Session token parameter [added](https://github.com/googlemaps/google-maps-services-python/pull/244) to `place()`.
- Fixed issue where headers in `request_kwargs` were being overridden.
### Added
- Automation for PyPi uploads.
- Long description to package.
- Added tests to manifest and tarball.
### Removed
- Removed places `places_autocomplete_session_token` which can be replaced with `uuid.uuid4().hex`.
- Removed deprecated `places_radar`.


**Note:** Start of changelog is 2019-08-27, [v3.0.2].

[Unreleased]: https://github.com/googlemaps/google-maps-services-python/compare/4.2.0...HEAD
[v4.2.0]: https://github.com/googlemaps/google-maps-services-python/compare/4.1.0...4.2.0
[v4.1.0]: https://github.com/googlemaps/google-maps-services-python/compare/4.0.0...4.1.0
[v4.0.0]: https://github.com/googlemaps/google-maps-services-python/compare/3.1.4...4.0.0
[v3.1.4]: https://github.com/googlemaps/google-maps-services-python/compare/3.1.3...3.1.4
[v3.1.3]: https://github.com/googlemaps/google-maps-services-python/compare/3.1.2...3.1.3
[v3.1.2]: https://github.com/googlemaps/google-maps-services-python/compare/3.1.1...3.1.2
[v3.1.1]: https://github.com/googlemaps/google-maps-services-python/compare/3.1.0...3.1.1
[v3.1.0]: https://github.com/googlemaps/google-maps-services-python/compare/3.0.2...3.1.0
[v3.0.2]: https://github.com/googlemaps/google-maps-services-python/compare/3.0.1...3.0.2
[v3.0.1]: https://github.com/googlemaps/google-maps-services-python/compare/3.0.0...3.0.1
[v3.0.0]: https://github.com/googlemaps/google-maps-services-python/compare/2.5.1...3.0.0
