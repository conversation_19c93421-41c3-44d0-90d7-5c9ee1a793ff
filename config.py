"""
Configuration settings for business data collection project
"""
import os
from typing import Optional
from dotenv import load_dotenv

load_dotenv()


class Settings:
    """Application settings"""

    def __init__(self):
        # Database configuration
        self.DATABASE_URL = os.getenv(
            "DATABASE_URL",
            "postgresql://username:password@localhost:5432/business_directory"
        )

        # API Keys
        self.GOOGLE_PLACES_API_KEY = os.getenv("GOOGLE_PLACES_API_KEY")
        self.YELP_API_KEY = os.getenv("YELP_API_KEY")

        # Geographic settings
        self.PITTSBURGH_LAT = 40.4406
        self.PITTSBURGH_LNG = -79.9959
        self.SEARCH_RADIUS_MILES = 300
        self.SEARCH_RADIUS_METERS = 482803  # 300 miles in meters

        # API Rate limiting
        self.GOOGLE_PLACES_REQUESTS_PER_SECOND = 10.0
        self.YELP_REQUESTS_PER_SECOND = 5.0
        self.MAX_RETRIES = 3
        self.RETRY_DELAY = 1.0

        # Data collection settings
        self.BATCH_SIZE = 100
        self.MAX_RESULTS_PER_SEARCH = 60  # Google Places API limit
        self.CONCURRENT_REQUESTS = 5

        # Business categories to search
        self.BUSINESS_CATEGORIES = [
        "restaurant",
        "store",
        "gas_station",
        "hospital",
        "pharmacy",
        "bank",
        "atm",
        "lawyer",
        "doctor",
        "dentist",
        "veterinary_care",
        "real_estate_agency",
        "insurance_agency",
        "accounting",
        "beauty_salon",
        "hair_care",
        "car_repair",
        "car_dealer",
        "lodging",
        "tourist_attraction",
        "gym",
        "school",
        "university",
        "library",
        "church",
        "shopping_mall",
        "supermarket",
        "clothing_store",
        "electronics_store",
        "furniture_store",
        "hardware_store",
        "florist",
        "bakery",
        "cafe",
        "bar",
        "night_club",
        "movie_theater",
        "amusement_park",
        "zoo",
        "museum",
        "art_gallery",
        "stadium",
        "rv_park",
        "campground",
        "spa",
        "taxi_stand",
        "subway_station",
        "train_station",
        "bus_station",
        "airport"
        ]

        # Geographic regions for systematic collection
        self.COLLECTION_REGIONS = [
        {
            "name": "Pittsburgh Metro",
            "lat": 40.4406,
            "lng": -79.9959,
            "radius_miles": 50,
            "priority": 1
        },
        {
            "name": "Philadelphia Metro", 
            "lat": 39.9526,
            "lng": -75.1652,
            "radius_miles": 50,
            "priority": 1
        },
        {
            "name": "Cleveland Metro",
            "lat": 41.4993,
            "lng": -81.6944,
            "radius_miles": 50,
            "priority": 2
        },
        {
            "name": "Columbus Metro",
            "lat": 39.9612,
            "lng": -82.9988,
            "radius_miles": 50,
            "priority": 2
        },
        {
            "name": "Baltimore Metro",
            "lat": 39.2904,
            "lng": -76.6122,
            "radius_miles": 50,
            "priority": 2
        },
        {
            "name": "Washington DC Metro",
            "lat": 38.9072,
            "lng": -77.0369,
            "radius_miles": 50,
            "priority": 2
        },
        {
            "name": "Buffalo Metro",
            "lat": 42.8864,
            "lng": -78.8784,
            "radius_miles": 50,
            "priority": 3
        },
        {
            "name": "Charleston Metro",
            "lat": 38.3498,
            "lng": -81.6326,
            "radius_miles": 50,
            "priority": 3
        }
        ]

        # Data quality settings
        self.MIN_BUSINESS_NAME_LENGTH = 2
        self.MAX_BUSINESS_NAME_LENGTH = 500
        self.DUPLICATE_SIMILARITY_THRESHOLD = 0.85

        # Export settings
        self.OUTPUT_DIRECTORY = "output"
        self.EXPORT_FORMATS = ["csv", "xlsx", "json"]

        # Logging
        self.LOG_LEVEL = "INFO"
        self.LOG_FILE = "business_collection.log"

        # Email extraction settings
        self.EXTRACT_EMAILS_FROM_WEBSITES = True
        self.WEBSITE_TIMEOUT_SECONDS = 10
        self.MAX_WEBSITE_PAGES_PER_BUSINESS = 3

# Global settings instance
settings = Settings()


# Major cities within 300 miles of Pittsburgh for reference
MAJOR_CITIES_300_MILES = [
    {"name": "Pittsburgh", "state": "PA", "lat": 40.4406, "lng": -79.9959, "distance": 0},
    {"name": "Philadelphia", "state": "PA", "lat": 39.9526, "lng": -75.1652, "distance": 300},
    {"name": "Cleveland", "state": "OH", "lat": 41.4993, "lng": -81.6944, "distance": 130},
    {"name": "Columbus", "state": "OH", "lat": 39.9612, "lng": -82.9988, "distance": 185},
    {"name": "Baltimore", "state": "MD", "lat": 39.2904, "lng": -76.6122, "distance": 250},
    {"name": "Washington", "state": "DC", "lat": 38.9072, "lng": -77.0369, "distance": 240},
    {"name": "Buffalo", "state": "NY", "lat": 42.8864, "lng": -78.8784, "distance": 220},
    {"name": "Charleston", "state": "WV", "lat": 38.3498, "lng": -81.6326, "distance": 200},
    {"name": "Morgantown", "state": "WV", "lat": 39.6295, "lng": -79.9553, "distance": 77},
    {"name": "Harrisburg", "state": "PA", "lat": 40.2732, "lng": -76.8839, "distance": 200},
    {"name": "Erie", "state": "PA", "lat": 42.1292, "lng": -80.0851, "distance": 130},
    {"name": "Akron", "state": "OH", "lat": 41.0814, "lng": -81.5190, "distance": 120},
    {"name": "Toledo", "state": "OH", "lat": 41.6528, "lng": -83.5379, "distance": 200},
    {"name": "Wheeling", "state": "WV", "lat": 40.0640, "lng": -80.7209, "distance": 60},
    {"name": "Allentown", "state": "PA", "lat": 40.6084, "lng": -75.4902, "distance": 280},
    {"name": "Scranton", "state": "PA", "lat": 41.4090, "lng": -75.6624, "distance": 280},
    {"name": "Youngstown", "state": "OH", "lat": 41.0998, "lng": -80.6495, "distance": 65},
    {"name": "Dayton", "state": "OH", "lat": 39.7589, "lng": -84.1916, "distance": 250},
    {"name": "Cincinnati", "state": "OH", "lat": 39.1031, "lng": -84.5120, "distance": 290},
    {"name": "Rochester", "state": "NY", "lat": 43.1566, "lng": -77.6088, "distance": 250}
]
