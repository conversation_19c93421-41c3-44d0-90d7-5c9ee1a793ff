"""
OpenStreetMap Overpass API Business Data Collector
Completely free alternative to Google Places API
"""
import requests
import json
import csv
import time
from datetime import datetime
from typing import List, Dict, Optional
import math
import re
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup


class OpenStreetMapCollector:
    """Collect business data from OpenStreetMap using Overpass API"""
    
    def __init__(self):
        self.base_url = "https://overpass-api.de/api/interpreter"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.timeout = 15

        # Social media patterns (same as your email_scraper.py)
        self.social_media_patterns = {
            'facebook': [
                r'(?:https?://)?(?:www\.)?facebook\.com/[^/\s]+',
                r'(?:https?://)?(?:www\.)?fb\.com/[^/\s]+',
                r'(?:https?://)?(?:m\.)?facebook\.com/[^/\s]+'
            ],
            'instagram': [
                r'(?:https?://)?(?:www\.)?instagram\.com/[^/\s]+',
                r'(?:https?://)?(?:www\.)?instagr\.am/[^/\s]+'
            ],
            'twitter': [
                r'(?:https?://)?(?:www\.)?twitter\.com/[^/\s]+',
                r'(?:https?://)?(?:www\.)?x\.com/[^/\s]+'
            ],
            'linkedin': [
                r'(?:https?://)?(?:www\.)?linkedin\.com/company/[^/\s]+',
                r'(?:https?://)?(?:www\.)?linkedin\.com/in/[^/\s]+'
            ],
            'youtube': [
                r'(?:https?://)?(?:www\.)?youtube\.com/channel/[^/\s]+',
                r'(?:https?://)?(?:www\.)?youtube\.com/c/[^/\s]+',
                r'(?:https?://)?(?:www\.)?youtube\.com/user/[^/\s]+',
                r'(?:https?://)?(?:www\.)?youtu\.be/[^/\s]+'
            ],
            'tiktok': [
                r'(?:https?://)?(?:www\.)?tiktok\.com/@[^/\s]+',
                r'(?:https?://)?(?:vm\.)?tiktok\.com/[^/\s]+'
            ],
            'pinterest': [
                r'(?:https?://)?(?:www\.)?pinterest\.com/[^/\s]+',
                r'(?:https?://)?(?:www\.)?pinterest\.co\.uk/[^/\s]+'
            ],
            'yelp': [
                r'(?:https?://)?(?:www\.)?yelp\.com/biz/[^/\s]+',
                r'(?:https?://)?(?:www\.)?yelp\.ca/biz/[^/\s]+'
            ],
            'google_business': [
                r'(?:https?://)?(?:www\.)?google\.com/maps/place/[^/\s]+',
                r'(?:https?://)?(?:goo\.gl/maps/[^/\s]+)',
                r'(?:https?://)?(?:maps\.google\.com/[^/\s]+)'
            ]
        }

        # Email regex patterns
        self.email_regex = re.compile(
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        )
        
    def calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """Calculate distance between two points in miles"""
        R = 3959  # Earth's radius in miles
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lng = math.radians(lng2 - lng1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lng / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c

    def clean_url(self, url: str) -> Optional[str]:
        """Clean and validate URL"""
        if not url:
            return None

        url = url.strip()
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        try:
            parsed = urlparse(url)
            if parsed.netloc:
                return url
        except:
            pass

        return None

    def detect_social_media_platform(self, url: str) -> Optional[str]:
        """Detect if URL is a social media platform"""
        if not url:
            return None

        url_lower = url.lower()

        social_domains = {
            'facebook.com': 'facebook',
            'fb.com': 'facebook',
            'm.facebook.com': 'facebook',
            'instagram.com': 'instagram',
            'instagr.am': 'instagram',
            'twitter.com': 'twitter',
            'x.com': 'twitter',
            'linkedin.com': 'linkedin',
            'youtube.com': 'youtube',
            'youtu.be': 'youtube',
            'tiktok.com': 'tiktok',
            'pinterest.com': 'pinterest',
            'yelp.com': 'yelp',
            'google.com/maps': 'google_business'
        }

        for domain, platform in social_domains.items():
            if domain in url_lower:
                return platform

        return None

    def extract_social_media_links(self, html_content: str, base_url: str) -> Dict[str, List[str]]:
        """Extract social media links from HTML content"""
        social_links = {platform: [] for platform in self.social_media_patterns.keys()}

        if not html_content:
            return social_links

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Find all links
            links = soup.find_all('a', href=True)

            for link in links:
                href = link.get('href', '')
                if not href:
                    continue

                # Convert relative URLs to absolute
                try:
                    full_url = urljoin(base_url, href)
                except:
                    full_url = href

                # Check against social media patterns
                for platform, patterns in self.social_media_patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, full_url, re.IGNORECASE):
                            if full_url not in social_links[platform]:
                                social_links[platform].append(full_url)
                            break

            # Also search in text content for social media URLs
            text_content = soup.get_text()
            for platform, patterns in self.social_media_patterns.items():
                for pattern in patterns:
                    matches = re.findall(pattern, text_content, re.IGNORECASE)
                    for match in matches:
                        if not match.startswith(('http://', 'https://')):
                            match = 'https://' + match
                        if match not in social_links[platform]:
                            social_links[platform].append(match)

        except Exception as e:
            print(f"Error extracting social media links: {e}")

        return social_links

    def extract_emails_from_text(self, text: str) -> List[str]:
        """Extract email addresses from text"""
        if not text:
            return []

        emails = self.email_regex.findall(text)

        # Filter out common non-business emails
        excluded_domains = [
            'example.com', 'test.com', 'gmail.com', 'yahoo.com',
            'hotmail.com', 'outlook.com', 'facebook.com', 'twitter.com'
        ]

        filtered_emails = []
        for email in emails:
            email = email.lower()
            if not any(excluded in email for excluded in excluded_domains):
                filtered_emails.append(email)

        return filtered_emails

    def scrape_website_for_emails_and_social(self, url: str, business_name: str) -> Dict:
        """Scrape website for emails and social media links"""
        result = {
            'emails': [],
            'social_links': {platform: [] for platform in self.social_media_patterns.keys()},
            'is_social_media': False,
            'social_platform': None
        }

        if not url:
            return result

        url = self.clean_url(url)
        if not url:
            return result

        print(f"  🔍 Scraping {business_name}: {url}")

        # Check if the URL itself is a social media page
        social_platform = self.detect_social_media_platform(url)
        if social_platform:
            result['is_social_media'] = True
            result['social_platform'] = social_platform
            result['social_links'][social_platform].append(url)
            print(f"    📱 Detected {social_platform} page")
            return result

        try:
            # Scrape the website
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()

            html_content = response.text

            # Extract emails
            emails = self.extract_emails_from_text(html_content)
            result['emails'] = emails

            # Extract social media links
            social_links = self.extract_social_media_links(html_content, url)
            result['social_links'] = social_links

            if emails:
                print(f"    ✅ Found {len(emails)} emails: {', '.join(emails[:3])}")

            # Count social media links found
            total_social = sum(len(links) for links in social_links.values())
            if total_social > 0:
                print(f"    📱 Found {total_social} social media links")

        except requests.exceptions.RequestException as e:
            print(f"    ❌ Error scraping website: {e}")
        except Exception as e:
            print(f"    ❌ Unexpected error: {e}")

        return result
    
    def build_overpass_query(self, center_lat: float, center_lng: float,
                           radius_km: float, business_types: List[str]) -> str:
        """Build Overpass API query for coffee businesses"""

        # Convert radius to meters
        radius_m = int(radius_km * 1000)

        # Simple, reliable coffee query
        query = f"""[out:json][timeout:180];
(
  node["amenity"="cafe"](around:{radius_m},{center_lat},{center_lng});
  way["amenity"="cafe"](around:{radius_m},{center_lat},{center_lng});
  node["shop"="coffee"](around:{radius_m},{center_lat},{center_lng});
  way["shop"="coffee"](around:{radius_m},{center_lat},{center_lng});
);
out center meta;"""

        return query
    
    def query_overpass(self, query: str) -> Optional[Dict]:
        """Execute Overpass API query"""
        try:
            response = self.session.post(
                self.base_url,
                data={'data': query},
                timeout=120
            )
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"Error querying Overpass API: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON response: {e}")
            return None
    
    def extract_business_data(self, element: Dict, center_lat: float, center_lng: float, scrape_websites: bool = True) -> Optional[Dict]:
        """Extract business information from OSM element with social media scraping"""
        tags = element.get('tags', {})

        # Skip if no name
        if 'name' not in tags:
            return None

        # Get coordinates
        if element['type'] == 'node':
            lat = element['lat']
            lng = element['lon']
        else:
            # For ways, use center coordinates
            if 'center' in element:
                lat = element['center']['lat']
                lng = element['center']['lon']
            else:
                return None

        # Calculate distance from Pittsburgh
        distance = self.calculate_distance(center_lat, center_lng, lat, lng)

        # Extract basic business information
        business_name = tags.get('name', '')
        website_url = tags.get('website', '')

        # Initialize business data
        business_data = {
            'business_name': business_name,
            'phone_number': tags.get('phone', ''),
            'email_address': tags.get('email', ''),
            'website_url': website_url,
            'full_address': self._build_address(tags),
            'city': tags.get('addr:city', ''),
            'state': tags.get('addr:state', ''),
            'zip_code': tags.get('addr:postcode', ''),
            'latitude': lat,
            'longitude': lng,
            'business_category': self._determine_category(tags),
            'business_hours': tags.get('opening_hours', ''),
            'distance_from_pittsburgh': round(distance, 1),
            'data_source': 'openstreetmap',
            'osm_id': element['id'],
            'osm_type': element['type'],
            'collected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),

            # Social media fields (same as your previous dataset)
            'facebook_url': '',
            'instagram_url': '',
            'twitter_url': '',
            'linkedin_url': '',
            'youtube_url': '',
            'tiktok_url': '',
            'pinterest_url': '',
            'yelp_url': '',
            'google_business_url': ''
        }

        # Scrape website for emails and social media if enabled
        if scrape_websites and website_url:
            try:
                scrape_result = self.scrape_website_for_emails_and_social(website_url, business_name)

                # Update email if found and not already present
                if scrape_result['emails'] and not business_data['email_address']:
                    business_data['email_address'] = scrape_result['emails'][0]

                # Update social media links
                for platform, links in scrape_result['social_links'].items():
                    if links:
                        if platform == 'facebook':
                            business_data['facebook_url'] = links[0]
                        elif platform == 'instagram':
                            business_data['instagram_url'] = links[0]
                        elif platform == 'twitter':
                            business_data['twitter_url'] = links[0]
                        elif platform == 'linkedin':
                            business_data['linkedin_url'] = links[0]
                        elif platform == 'youtube':
                            business_data['youtube_url'] = links[0]
                        elif platform == 'tiktok':
                            business_data['tiktok_url'] = links[0]
                        elif platform == 'pinterest':
                            business_data['pinterest_url'] = links[0]
                        elif platform == 'yelp':
                            business_data['yelp_url'] = links[0]
                        elif platform == 'google_business':
                            business_data['google_business_url'] = links[0]

                # If the website itself is a social media page, update accordingly
                if scrape_result['is_social_media']:
                    platform = scrape_result['social_platform']
                    if platform == 'facebook':
                        business_data['facebook_url'] = website_url
                        business_data['website_url'] = ''  # Clear website since it's actually Facebook
                    elif platform == 'instagram':
                        business_data['instagram_url'] = website_url
                        business_data['website_url'] = ''
                    # Add other platforms as needed

                time.sleep(1)  # Be respectful with scraping

            except Exception as e:
                print(f"    ❌ Error scraping {business_name}: {e}")

        return business_data
    
    def _build_address(self, tags: Dict) -> str:
        """Build full address from OSM tags"""
        parts = []
        
        if 'addr:housenumber' in tags:
            parts.append(tags['addr:housenumber'])
        if 'addr:street' in tags:
            parts.append(tags['addr:street'])
            
        return ' '.join(parts) if parts else ''
    
    def _determine_category(self, tags: Dict) -> str:
        """Determine business category from OSM tags"""
        if 'amenity' in tags:
            return f"amenity:{tags['amenity']}"
        elif 'shop' in tags:
            return f"shop:{tags['shop']}"
        elif 'office' in tags:
            return f"office:{tags['office']}"
        elif 'craft' in tags:
            return f"craft:{tags['craft']}"
        else:
            return 'unknown'
    
    def collect_businesses_in_radius(self, center_lat: float, center_lng: float, 
                                   radius_miles: float, business_types: List[str]) -> List[Dict]:
        """Collect all businesses within radius"""
        
        # Convert miles to km
        radius_km = radius_miles * 1.60934
        
        print(f"Collecting businesses within {radius_miles} miles of ({center_lat}, {center_lng})")
        print(f"Business types: {', '.join(business_types)}")
        
        # Build and execute query
        query = self.build_overpass_query(center_lat, center_lng, radius_km, business_types)
        
        print("Executing Overpass API query...")
        result = self.query_overpass(query)
        
        if not result:
            print("Failed to get data from Overpass API")
            return []
        
        elements = result.get('elements', [])
        print(f"Found {len(elements)} raw elements")
        
        # Extract business data with social media scraping
        businesses = []

        for i, element in enumerate(elements):
            if i % 100 == 0:  # Progress update every 100 businesses
                print(f"Processing business {i+1}/{len(elements)}")
            business_data = self.extract_business_data(element, center_lat, center_lng, scrape_websites=True)
            if business_data:
                businesses.append(business_data)
        
        print(f"Extracted {len(businesses)} valid businesses")
        return businesses
    
    def save_to_csv(self, businesses: List[Dict], filename: str):
        """Save businesses to CSV file"""
        if not businesses:
            print("No businesses to save")
            return
            
        fieldnames = [
            'business_name', 'phone_number', 'email_address', 'website_url',
            'full_address', 'city', 'state', 'zip_code', 'latitude', 'longitude',
            'business_category', 'business_hours', 'distance_from_pittsburgh',
            'facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url',
            'youtube_url', 'tiktok_url', 'pinterest_url', 'yelp_url', 'google_business_url',
            'data_source', 'osm_id', 'osm_type', 'collected_at'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(businesses)
        
        print(f"Saved {len(businesses)} businesses to {filename}")


    def is_chain_coffee_shop(self, business_name: str) -> bool:
        """Check if business is a chain coffee shop to filter out"""
        if not business_name:
            return False

        name_lower = business_name.lower()

        # Chain coffee shops to exclude (same as your previous dataset)
        chains = [
            'starbucks', 'dunkin', 'tim hortons', 'tim horton',
            'mcdonalds', 'burger king', 'subway', 'kfc',
            'taco bell', 'pizza hut', 'dominos', 'papa johns',
            'wendys', 'arbys', 'dairy queen', 'sonic',
            'panera', 'chipotle', 'qdoba', 'five guys',
            'jimmy johns', 'jersey mikes', 'firehouse subs',
            'einstein bros', 'caribou coffee', 'peets coffee',
            'costa coffee', 'second cup', 'gloria jeans'
        ]

        for chain in chains:
            if chain in name_lower:
                return True

        return False

    def filter_coffee_businesses(self, businesses: List[Dict]) -> List[Dict]:
        """Filter for independent coffee businesses only"""
        coffee_businesses = []

        for business in businesses:
            category = business.get('business_category', '').lower()
            name = business.get('business_name', '')

            # Check if it's a coffee-related business
            is_coffee = any(keyword in category for keyword in [
                'cafe', 'coffee', 'espresso', 'roaster', 'roastery'
            ])

            # Also check business name for coffee keywords
            if not is_coffee and name:
                name_lower = name.lower()
                is_coffee = any(keyword in name_lower for keyword in [
                    'coffee', 'cafe', 'espresso', 'roaster', 'roastery',
                    'bean', 'brew', 'grind', 'latte', 'cappuccino'
                ])

            # Include if it's coffee-related and not a chain
            if is_coffee and not self.is_chain_coffee_shop(name):
                business['business_category'] = 'Independent Coffee Shop/Cafe'
                coffee_businesses.append(business)

        return coffee_businesses

def main():
    """Collect independent coffee businesses within 300 miles of Pittsburgh"""
    collector = OpenStreetMapCollector()

    # Pittsburgh coordinates
    pittsburgh_lat = 40.4406
    pittsburgh_lng = -79.9959

    # Coffee-specific business types for OSM
    coffee_business_types = [
        'cafe', 'coffee_shop', 'restaurant'  # OSM amenity types
    ]

    print("🔍 Collecting independent coffee businesses from OpenStreetMap...")
    print("📍 Center: Pittsburgh, PA")
    print("📏 Radius: 300 miles")
    print("☕ Focus: Independent coffee shops (chains excluded)")

    # Collect businesses within 300 miles with social media scraping
    print("Collecting within 300-mile radius with social media scraping...")
    all_businesses = collector.collect_businesses_in_radius(
        pittsburgh_lat, pittsburgh_lng, 300, coffee_business_types
    )

    print(f"📊 Found {len(all_businesses)} total businesses")

    # Filter for independent coffee businesses
    coffee_businesses = collector.filter_coffee_businesses(all_businesses)

    print(f"☕ Filtered to {len(coffee_businesses)} independent coffee businesses")

    # Save to CSV
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"openstreetmap_independent_coffee_{timestamp}.csv"
    collector.save_to_csv(coffee_businesses, filename)

    return filename


if __name__ == "__main__":
    main()
