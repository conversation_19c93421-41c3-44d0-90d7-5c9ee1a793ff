"""
OpenStreetMap Overpass API Business Data Collector
Completely free alternative to Google Places API
"""
import requests
import json
import csv
import time
from datetime import datetime
from typing import List, Dict, Optional
import math


class OpenStreetMapCollector:
    """Collect business data from OpenStreetMap using Overpass API"""
    
    def __init__(self):
        self.base_url = "https://overpass-api.de/api/interpreter"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Business Data Collector/1.0'
        })
        
    def calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """Calculate distance between two points in miles"""
        R = 3959  # Earth's radius in miles
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lng = math.radians(lng2 - lng1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lng / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def build_overpass_query(self, center_lat: float, center_lng: float,
                           radius_km: float, business_types: List[str]) -> str:
        """Build Overpass API query for coffee businesses"""

        # Convert radius to meters
        radius_m = int(radius_km * 1000)

        # Simple, reliable coffee query
        query = f"""[out:json][timeout:180];
(
  node["amenity"="cafe"](around:{radius_m},{center_lat},{center_lng});
  way["amenity"="cafe"](around:{radius_m},{center_lat},{center_lng});
  node["shop"="coffee"](around:{radius_m},{center_lat},{center_lng});
  way["shop"="coffee"](around:{radius_m},{center_lat},{center_lng});
);
out center meta;"""

        return query
    
    def query_overpass(self, query: str) -> Optional[Dict]:
        """Execute Overpass API query"""
        try:
            response = self.session.post(
                self.base_url,
                data={'data': query},
                timeout=120
            )
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"Error querying Overpass API: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON response: {e}")
            return None
    
    def extract_business_data(self, element: Dict, center_lat: float, center_lng: float) -> Optional[Dict]:
        """Extract business information from OSM element"""
        tags = element.get('tags', {})
        
        # Skip if no name
        if 'name' not in tags:
            return None
            
        # Get coordinates
        if element['type'] == 'node':
            lat = element['lat']
            lng = element['lon']
        else:
            # For ways, use center coordinates
            if 'center' in element:
                lat = element['center']['lat']
                lng = element['center']['lon']
            else:
                return None
        
        # Calculate distance from Pittsburgh
        distance = self.calculate_distance(center_lat, center_lng, lat, lng)
        
        # Extract business information
        business_data = {
            'business_name': tags.get('name', ''),
            'phone_number': tags.get('phone', ''),
            'email_address': tags.get('email', ''),
            'website_url': tags.get('website', ''),
            'full_address': self._build_address(tags),
            'city': tags.get('addr:city', ''),
            'state': tags.get('addr:state', ''),
            'zip_code': tags.get('addr:postcode', ''),
            'latitude': lat,
            'longitude': lng,
            'business_category': self._determine_category(tags),
            'business_hours': tags.get('opening_hours', ''),
            'distance_from_pittsburgh': round(distance, 1),
            'data_source': 'openstreetmap',
            'osm_id': element['id'],
            'osm_type': element['type'],
            'collected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return business_data
    
    def _build_address(self, tags: Dict) -> str:
        """Build full address from OSM tags"""
        parts = []
        
        if 'addr:housenumber' in tags:
            parts.append(tags['addr:housenumber'])
        if 'addr:street' in tags:
            parts.append(tags['addr:street'])
            
        return ' '.join(parts) if parts else ''
    
    def _determine_category(self, tags: Dict) -> str:
        """Determine business category from OSM tags"""
        if 'amenity' in tags:
            return f"amenity:{tags['amenity']}"
        elif 'shop' in tags:
            return f"shop:{tags['shop']}"
        elif 'office' in tags:
            return f"office:{tags['office']}"
        elif 'craft' in tags:
            return f"craft:{tags['craft']}"
        else:
            return 'unknown'
    
    def collect_businesses_in_radius(self, center_lat: float, center_lng: float, 
                                   radius_miles: float, business_types: List[str]) -> List[Dict]:
        """Collect all businesses within radius"""
        
        # Convert miles to km
        radius_km = radius_miles * 1.60934
        
        print(f"Collecting businesses within {radius_miles} miles of ({center_lat}, {center_lng})")
        print(f"Business types: {', '.join(business_types)}")
        
        # Build and execute query
        query = self.build_overpass_query(center_lat, center_lng, radius_km, business_types)
        
        print("Executing Overpass API query...")
        result = self.query_overpass(query)
        
        if not result:
            print("Failed to get data from Overpass API")
            return []
        
        elements = result.get('elements', [])
        print(f"Found {len(elements)} raw elements")
        
        # Extract business data
        businesses = []
        for element in elements:
            business_data = self.extract_business_data(element, center_lat, center_lng)
            if business_data:
                businesses.append(business_data)
        
        print(f"Extracted {len(businesses)} valid businesses")
        return businesses
    
    def save_to_csv(self, businesses: List[Dict], filename: str):
        """Save businesses to CSV file"""
        if not businesses:
            print("No businesses to save")
            return
            
        fieldnames = [
            'business_name', 'phone_number', 'email_address', 'website_url',
            'full_address', 'city', 'state', 'zip_code', 'latitude', 'longitude',
            'business_category', 'business_hours', 'distance_from_pittsburgh',
            'data_source', 'osm_id', 'osm_type', 'collected_at'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(businesses)
        
        print(f"Saved {len(businesses)} businesses to {filename}")


    def is_chain_coffee_shop(self, business_name: str) -> bool:
        """Check if business is a chain coffee shop to filter out"""
        if not business_name:
            return False

        name_lower = business_name.lower()

        # Chain coffee shops to exclude (same as your previous dataset)
        chains = [
            'starbucks', 'dunkin', 'tim hortons', 'tim horton',
            'mcdonalds', 'burger king', 'subway', 'kfc',
            'taco bell', 'pizza hut', 'dominos', 'papa johns',
            'wendys', 'arbys', 'dairy queen', 'sonic',
            'panera', 'chipotle', 'qdoba', 'five guys',
            'jimmy johns', 'jersey mikes', 'firehouse subs',
            'einstein bros', 'caribou coffee', 'peets coffee',
            'costa coffee', 'second cup', 'gloria jeans'
        ]

        for chain in chains:
            if chain in name_lower:
                return True

        return False

    def filter_coffee_businesses(self, businesses: List[Dict]) -> List[Dict]:
        """Filter for independent coffee businesses only"""
        coffee_businesses = []

        for business in businesses:
            category = business.get('business_category', '').lower()
            name = business.get('business_name', '')

            # Check if it's a coffee-related business
            is_coffee = any(keyword in category for keyword in [
                'cafe', 'coffee', 'espresso', 'roaster', 'roastery'
            ])

            # Also check business name for coffee keywords
            if not is_coffee and name:
                name_lower = name.lower()
                is_coffee = any(keyword in name_lower for keyword in [
                    'coffee', 'cafe', 'espresso', 'roaster', 'roastery',
                    'bean', 'brew', 'grind', 'latte', 'cappuccino'
                ])

            # Include if it's coffee-related and not a chain
            if is_coffee and not self.is_chain_coffee_shop(name):
                business['business_category'] = 'Independent Coffee Shop/Cafe'
                coffee_businesses.append(business)

        return coffee_businesses

def main():
    """Collect independent coffee businesses within 300 miles of Pittsburgh"""
    collector = OpenStreetMapCollector()

    # Pittsburgh coordinates
    pittsburgh_lat = 40.4406
    pittsburgh_lng = -79.9959

    # Coffee-specific business types for OSM
    coffee_business_types = [
        'cafe', 'coffee_shop', 'restaurant'  # OSM amenity types
    ]

    print("🔍 Collecting independent coffee businesses from OpenStreetMap...")
    print("📍 Center: Pittsburgh, PA")
    print("📏 Radius: 300 miles")
    print("☕ Focus: Independent coffee shops (chains excluded)")

    # Collect businesses within 300 miles (same as previous dataset)
    print("Collecting within full 300-mile radius...")
    all_businesses = collector.collect_businesses_in_radius(
        pittsburgh_lat, pittsburgh_lng, 300, coffee_business_types
    )

    print(f"📊 Found {len(all_businesses)} total businesses")

    # Filter for independent coffee businesses
    coffee_businesses = collector.filter_coffee_businesses(all_businesses)

    print(f"☕ Filtered to {len(coffee_businesses)} independent coffee businesses")

    # Save to CSV
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"openstreetmap_independent_coffee_{timestamp}.csv"
    collector.save_to_csv(coffee_businesses, filename)

    return filename


if __name__ == "__main__":
    main()
