#!/usr/bin/env python3
"""
Direct business collection script for Pittsburgh area
This script collects business data and exports directly to CSV
"""
import os
import time
import math
import csv
from datetime import datetime
import googlemaps
from config import settings


def calculate_distance(lat1, lng1, lat2, lng2):
    """Calculate distance between two points using Haversine formula"""
    R = 3959  # Earth's radius in miles
    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lng = math.radians(lng2 - lng1)
    
    a = (math.sin(delta_lat / 2) ** 2 + 
         math.cos(lat1_rad) * math.cos(lat2_rad) * 
         math.sin(delta_lng / 2) ** 2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    
    return R * c


def is_within_radius(lat, lng, center_lat=40.4406, center_lng=-79.9959, radius_miles=300):
    """Check if coordinates are within specified radius of center point"""
    distance = calculate_distance(center_lat, center_lng, lat, lng)
    return distance <= radius_miles, distance


def collect_businesses_in_region(client, center_lat, center_lng, region_name, radius_km=80):
    """Collect businesses in a specific region"""
    print(f"\n🔍 Searching in {region_name}...")
    
    businesses = []
    business_types = [
        'restaurant', 'store', 'gas_station', 'hospital', 'pharmacy', 'bank',
        'lawyer', 'doctor', 'dentist', 'real_estate_agency', 'insurance_agency',
        'accounting', 'beauty_salon', 'car_repair', 'lodging', 'gym', 'school'
    ]
    
    for business_type in business_types:
        try:
            print(f"  Searching for {business_type}...")
            
            # Search for businesses of this type
            result = client.places_nearby(
                location=(center_lat, center_lng),
                radius=radius_km * 1000,  # Convert km to meters
                type=business_type,
                language='en'
            )
            
            if result['status'] == 'OK':
                places = result.get('results', [])
                print(f"    Found {len(places)} {business_type} businesses")
                
                for place in places:
                    try:
                        # Get basic info
                        place_id = place.get('place_id')
                        name = place.get('name', '')
                        location = place.get('geometry', {}).get('location', {})
                        lat = location.get('lat')
                        lng = location.get('lng')
                        
                        if not lat or not lng:
                            continue
                            
                        # Check if within 300-mile radius of Pittsburgh
                        within_radius, distance = is_within_radius(lat, lng)
                        if not within_radius:
                            continue
                            
                        # Get detailed information
                        details = client.place(
                            place_id=place_id,
                            fields=['name', 'formatted_address', 'formatted_phone_number', 
                                   'website', 'rating', 'user_ratings_total']
                        )
                        
                        if details['status'] == 'OK':
                            detail_info = details['result']
                            
                            business_data = {
                                'business_name': detail_info.get('name', name),
                                'phone_number': detail_info.get('formatted_phone_number', ''),
                                'email_address': '',  # Will be empty - not available via API
                                'website_url': detail_info.get('website', ''),
                                'full_address': detail_info.get('formatted_address', ''),
                                'latitude': lat,
                                'longitude': lng,
                                'business_category': business_type,
                                'google_rating': detail_info.get('rating', ''),
                                'google_review_count': detail_info.get('user_ratings_total', ''),
                                'distance_from_pittsburgh': round(distance, 1),
                                'data_source': 'google_places',
                                'collection_region': region_name,
                                'collected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }
                            
                            businesses.append(business_data)
                            
                        # Rate limiting
                        time.sleep(0.1)
                        
                    except Exception as e:
                        print(f"    Error processing place: {e}")
                        continue
                        
            # Rate limiting between searches
            time.sleep(1)
            
        except Exception as e:
            print(f"  Error searching for {business_type}: {e}")
            continue
    
    print(f"✅ Collected {len(businesses)} businesses from {region_name}")
    return businesses


def main():
    """Main collection process"""
    print("=" * 70)
    print("🏢 PITTSBURGH AREA BUSINESS COLLECTION")
    print("=" * 70)
    print(f"📍 Target: 300-mile radius from Pittsburgh, PA")
    print(f"🔑 Using Google Places API")
    print()
    
    # Initialize Google Maps client
    if not settings.GOOGLE_PLACES_API_KEY:
        print("❌ Google Places API key not found!")
        return
        
    client = googlemaps.Client(key=settings.GOOGLE_PLACES_API_KEY)
    
    # Define collection regions (major metropolitan areas within 300 miles)
    regions = [
        {"name": "Pittsburgh Metro", "lat": 40.4406, "lng": -79.9959, "radius": 50},
        {"name": "Philadelphia Metro", "lat": 39.9526, "lng": -75.1652, "radius": 50},
        {"name": "Cleveland Metro", "lat": 41.4993, "lng": -81.6944, "radius": 40},
        {"name": "Columbus Metro", "lat": 39.9612, "lng": -82.9988, "radius": 40},
        {"name": "Baltimore Metro", "lat": 39.2904, "lng": -76.6122, "radius": 40},
        {"name": "Washington DC Metro", "lat": 38.9072, "lng": -77.0369, "radius": 40},
        {"name": "Buffalo Metro", "lat": 42.8864, "lng": -78.8784, "radius": 30},
        {"name": "Charleston Metro", "lat": 38.3498, "lng": -81.6326, "radius": 30},
        {"name": "Harrisburg Metro", "lat": 40.2732, "lng": -76.8839, "radius": 30},
        {"name": "Erie Metro", "lat": 42.1292, "lng": -80.0851, "radius": 25},
        {"name": "Wheeling Metro", "lat": 40.0640, "lng": -80.7209, "radius": 25},
        {"name": "Morgantown Metro", "lat": 39.6295, "lng": -79.9553, "radius": 25}
    ]
    
    all_businesses = []
    
    # Collect from each region
    for region in regions:
        try:
            businesses = collect_businesses_in_region(
                client, 
                region["lat"], 
                region["lng"], 
                region["name"],
                region["radius"]
            )
            all_businesses.extend(businesses)
            
            print(f"📊 Total businesses collected so far: {len(all_businesses)}")
            
            # Brief pause between regions
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ Error collecting from {region['name']}: {e}")
            continue
    
    # Remove duplicates based on name and phone
    print(f"\n🔄 Removing duplicates...")
    unique_businesses = []
    seen = set()
    
    for business in all_businesses:
        # Create a key for deduplication
        key = (
            business['business_name'].lower().strip(),
            business['phone_number'].strip(),
            business['full_address'].lower().strip()
        )
        
        if key not in seen:
            seen.add(key)
            unique_businesses.append(business)
    
    print(f"✅ Removed {len(all_businesses) - len(unique_businesses)} duplicates")
    print(f"📊 Final count: {len(unique_businesses)} unique businesses")
    
    # Export to CSV
    if unique_businesses:
        # Create output directory
        os.makedirs('output', exist_ok=True)
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"output/pittsburgh_businesses_{timestamp}.csv"
        
        # Write to CSV
        fieldnames = [
            'business_name', 'phone_number', 'email_address', 'website_url',
            'full_address', 'latitude', 'longitude', 'business_category',
            'google_rating', 'google_review_count', 'distance_from_pittsburgh',
            'data_source', 'collection_region', 'collected_at'
        ]
        
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(unique_businesses)
        
        print(f"\n🎉 SUCCESS! Data exported to: {csv_filename}")
        
        # Generate summary
        with_phone = sum(1 for b in unique_businesses if b['phone_number'])
        with_website = sum(1 for b in unique_businesses if b['website_url'])
        avg_distance = sum(float(b['distance_from_pittsburgh']) for b in unique_businesses) / len(unique_businesses)
        
        print(f"\n📈 COLLECTION SUMMARY:")
        print(f"Total businesses: {len(unique_businesses):,}")
        print(f"Businesses with phone: {with_phone:,} ({with_phone/len(unique_businesses)*100:.1f}%)")
        print(f"Businesses with website: {with_website:,} ({with_website/len(unique_businesses)*100:.1f}%)")
        print(f"Average distance from Pittsburgh: {avg_distance:.1f} miles")
        
        # Show breakdown by region
        from collections import Counter
        region_counts = Counter(b['collection_region'] for b in unique_businesses)
        print(f"\n📍 BUSINESSES BY REGION:")
        for region, count in region_counts.most_common():
            print(f"  {region}: {count:,}")
            
        # Show breakdown by category
        category_counts = Counter(b['business_category'] for b in unique_businesses)
        print(f"\n🏷️ TOP BUSINESS CATEGORIES:")
        for category, count in category_counts.most_common(10):
            print(f"  {category}: {count:,}")
    
    else:
        print("❌ No businesses collected!")
    
    print(f"\n✅ Collection complete!")


if __name__ == '__main__':
    main()
