"""
Government Business Database Collector
Collect business data from free government sources
"""
import requests
import csv
from bs4 import BeautifulSoup
import time
import re
from datetime import datetime
from typing import List, Dict, Optional
import json


class GovernmentDataCollector:
    """Collect business data from government databases"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # State business search URLs (verified free access)
        self.state_databases = {
            'PA': {
                'name': 'Pennsylvania Department of State',
                'search_url': 'https://www.corporations.pa.gov/search/corpsearch',
                'method': 'web_scraping'
            },
            'OH': {
                'name': 'Ohio Secretary of State',
                'search_url': 'https://www5.sos.state.oh.us/ords/f?p=100:7:0::NO:7:P7_CHARTER_NUM:',
                'method': 'web_scraping'
            },
            'WV': {
                'name': 'West Virginia Secretary of State',
                'search_url': 'https://apps.sos.wv.gov/business/corporations/',
                'method': 'web_scraping'
            },
            'MD': {
                'name': 'Maryland Department of Assessments',
                'search_url': 'https://egov.maryland.gov/BusinessExpress/EntitySearch',
                'method': 'web_scraping'
            }
        }
        
        # Federal data sources
        self.federal_sources = {
            'sba': {
                'name': 'Small Business Administration',
                'api_url': 'https://api.sba.gov/v1/loans/foia',
                'method': 'api'
            },
            'census': {
                'name': 'US Census Business Patterns',
                'api_url': 'https://api.census.gov/data/2021/cbp',
                'method': 'api'
            }
        }
    
    def search_pa_corporations(self, business_name: str) -> List[Dict]:
        """Search Pennsylvania corporation database"""
        businesses = []
        
        try:
            # This is a simplified example - actual implementation would need
            # to handle the specific form submission and parsing for PA
            search_url = "https://www.corporations.pa.gov/search/corpsearch"
            
            # For demonstration - in practice you'd need to handle the specific
            # form structure and CSRF tokens
            print(f"Searching PA database for: {business_name}")
            
            # Placeholder for actual implementation
            # Real implementation would:
            # 1. Get the search form
            # 2. Submit search with business name
            # 3. Parse results table
            # 4. Extract business details
            
            # Example result structure
            example_result = {
                'business_name': business_name,
                'state': 'PA',
                'registration_number': 'Example123',
                'status': 'Active',
                'registered_address': 'Example Address',
                'data_source': 'pa_corporations'
            }
            
            businesses.append(example_result)
            
        except Exception as e:
            print(f"Error searching PA database: {e}")
        
        return businesses
    
    def search_census_business_patterns(self, state_code: str, county_code: str = None) -> List[Dict]:
        """Search US Census Business Patterns API"""
        businesses = []
        
        try:
            # Census API endpoint for County Business Patterns
            base_url = "https://api.census.gov/data/2021/cbp"
            
            # Parameters for business data
            params = {
                'get': 'NAME,NAICS2017_LABEL,EMP,ESTAB,PAYANN',
                'for': f'state:{state_code}',
                'NAICS2017': '*'  # All industries
            }
            
            if county_code:
                params['for'] = f'county:{county_code}'
                params['in'] = f'state:{state_code}'
            
            response = self.session.get(base_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Parse census data
            if len(data) > 1:  # First row is headers
                headers = data[0]
                for row in data[1:]:
                    business_data = dict(zip(headers, row))
                    
                    # Convert to our format
                    formatted_data = {
                        'business_name': business_data.get('NAME', ''),
                        'industry': business_data.get('NAICS2017_LABEL', ''),
                        'employees': business_data.get('EMP', ''),
                        'establishments': business_data.get('ESTAB', ''),
                        'annual_payroll': business_data.get('PAYANN', ''),
                        'state_code': state_code,
                        'data_source': 'census_business_patterns',
                        'collected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    businesses.append(formatted_data)
            
        except Exception as e:
            print(f"Error accessing Census API: {e}")
        
        return businesses
    
    def search_data_gov_datasets(self, query: str) -> List[Dict]:
        """Search Data.gov for business datasets"""
        businesses = []
        
        try:
            # Data.gov CKAN API
            api_url = "https://catalog.data.gov/api/3/action/package_search"
            
            params = {
                'q': f'business {query}',
                'rows': 20,
                'sort': 'score desc'
            }
            
            response = self.session.get(api_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data['success']:
                datasets = data['result']['results']
                
                for dataset in datasets:
                    # Look for CSV resources
                    for resource in dataset.get('resources', []):
                        if resource.get('format', '').lower() == 'csv':
                            dataset_info = {
                                'dataset_name': dataset.get('title', ''),
                                'description': dataset.get('notes', ''),
                                'csv_url': resource.get('url', ''),
                                'organization': dataset.get('organization', {}).get('title', ''),
                                'data_source': 'data_gov',
                                'last_modified': resource.get('last_modified', ''),
                                'collected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }
                            
                            businesses.append(dataset_info)
            
        except Exception as e:
            print(f"Error searching Data.gov: {e}")
        
        return businesses
    
    def collect_yellow_pages_data(self, location: str, category: str) -> List[Dict]:
        """Collect data from Yellow Pages (public listings)"""
        businesses = []
        
        try:
            # Yellow Pages search URL
            search_url = f"https://www.yellowpages.com/search"
            
            params = {
                'search_terms': category,
                'geo_location_terms': location
            }
            
            response = self.session.get(search_url, params=params, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Parse Yellow Pages results
            listings = soup.find_all('div', class_='result')
            
            for listing in listings[:20]:  # Limit to first 20 results
                try:
                    name_elem = listing.find('a', class_='business-name')
                    phone_elem = listing.find('div', class_='phones')
                    address_elem = listing.find('div', class_='street-address')
                    
                    business_data = {
                        'business_name': name_elem.text.strip() if name_elem else '',
                        'phone_number': phone_elem.text.strip() if phone_elem else '',
                        'address': address_elem.text.strip() if address_elem else '',
                        'category': category,
                        'location': location,
                        'data_source': 'yellow_pages',
                        'collected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    if business_data['business_name']:
                        businesses.append(business_data)
                        
                except Exception as e:
                    print(f"Error parsing listing: {e}")
                    continue
            
            time.sleep(2)  # Be respectful
            
        except Exception as e:
            print(f"Error scraping Yellow Pages: {e}")
        
        return businesses
    
    def collect_bbb_data(self, location: str, category: str) -> List[Dict]:
        """Collect data from Better Business Bureau"""
        businesses = []
        
        try:
            # BBB search URL
            search_url = "https://www.bbb.org/search"
            
            params = {
                'find_text': category,
                'find_loc': location,
                'find_type': 'Category'
            }
            
            response = self.session.get(search_url, params=params, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Parse BBB results (simplified example)
            listings = soup.find_all('div', class_='search-result-content')
            
            for listing in listings[:15]:  # Limit results
                try:
                    name_elem = listing.find('h3')
                    rating_elem = listing.find('div', class_='bbb-rating')
                    
                    business_data = {
                        'business_name': name_elem.text.strip() if name_elem else '',
                        'bbb_rating': rating_elem.text.strip() if rating_elem else '',
                        'category': category,
                        'location': location,
                        'data_source': 'better_business_bureau',
                        'collected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    if business_data['business_name']:
                        businesses.append(business_data)
                        
                except Exception as e:
                    continue
            
            time.sleep(3)  # Be respectful
            
        except Exception as e:
            print(f"Error scraping BBB: {e}")
        
        return businesses
    
    def collect_all_government_data(self, location: str, categories: List[str]) -> List[Dict]:
        """Collect data from all available government sources"""
        all_businesses = []
        
        print(f"Collecting government data for {location}")
        
        # Collect from various sources
        for category in categories:
            print(f"Searching for {category} businesses...")
            
            # Yellow Pages
            yp_businesses = self.collect_yellow_pages_data(location, category)
            all_businesses.extend(yp_businesses)
            
            # BBB
            bbb_businesses = self.collect_bbb_data(location, category)
            all_businesses.extend(bbb_businesses)
            
            time.sleep(2)  # Rate limiting
        
        # Census data (state level)
        state_codes = {'Pennsylvania': '42', 'Ohio': '39', 'Maryland': '24', 'West Virginia': '54'}
        for state_name, code in state_codes.items():
            if state_name.lower() in location.lower():
                census_data = self.search_census_business_patterns(code)
                all_businesses.extend(census_data)
                break
        
        # Data.gov datasets
        datagov_datasets = self.search_data_gov_datasets(location)
        all_businesses.extend(datagov_datasets)
        
        return all_businesses
    
    def save_to_csv(self, businesses: List[Dict], filename: str):
        """Save collected data to CSV"""
        if not businesses:
            print("No data to save")
            return
        
        # Get all unique fieldnames
        fieldnames = set()
        for business in businesses:
            fieldnames.update(business.keys())
        
        fieldnames = sorted(list(fieldnames))
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(businesses)
        
        print(f"Saved {len(businesses)} records to {filename}")


def main():
    """Example usage"""
    collector = GovernmentDataCollector()
    
    # Collect data for Pittsburgh area
    location = "Pittsburgh, PA"
    categories = ["restaurant", "retail", "services", "healthcare"]
    
    businesses = collector.collect_all_government_data(location, categories)
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"government_business_data_{timestamp}.csv"
    collector.save_to_csv(businesses, filename)


if __name__ == "__main__":
    main()
