Levenshtein/StringMatcher.py,sha256=9_EPf14D3atldYwG0iViJRSADMBoNUbgwZNUsvwLqrc,2359
Levenshtein/__init__.py,sha256=8Etz3C09ZRgywlyYFcGsnz3fEremlRjZ2YdXkTw0sl8,17933
Levenshtein/__init__.pyi,sha256=URApxflviIsMw-RTDNLtpRKUl0a2LF0zUtIqBK-WRGE,2918
Levenshtein/__pycache__/StringMatcher.cpython-313.pyc,,
Levenshtein/__pycache__/__init__.cpython-313.pyc,,
Levenshtein/levenshtein_cpp.cp313-win_amd64.pyd,sha256=k9c0QInL6R0wOGiyBB9aDgIgVxIEGyPWqpH8lFG2o6Q,229888
Levenshtein/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
levenshtein-0.27.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
levenshtein-0.27.1.dist-info/METADATA,sha256=osc_0j08XY0SqdbFVD0x1iybgTXZSeY6LV563TtzDWI,3555
levenshtein-0.27.1.dist-info/RECORD,,
levenshtein-0.27.1.dist-info/WHEEL,sha256=cLLOEDLyjCILsZDMu27mnm9MvT0HdHpLhnFPBmwlQ6E,106
levenshtein-0.27.1.dist-info/licenses/COPYING,sha256=GsTA4NwEjHfg0vDxz0dEifxVtAem86QBgE2ZJAxfbTc,18080
