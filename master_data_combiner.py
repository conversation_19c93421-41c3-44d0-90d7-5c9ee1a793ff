"""
Master Data Combiner
Combine data from all free sources into a unified CSV output
"""
import pandas as pd
import csv
from datetime import datetime
from typing import List, Dict, Optional
import os
import math


class MasterDataCombiner:
    """Combine and deduplicate business data from multiple sources"""
    
    def __init__(self):
        self.pittsburgh_lat = 40.4406
        self.pittsburgh_lng = -79.9959
        
    def calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """Calculate distance between two points in miles"""
        if pd.isna(lat1) or pd.isna(lng1) or pd.isna(lat2) or pd.isna(lng2):
            return float('inf')
            
        R = 3959  # Earth's radius in miles
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lng = math.radians(lng2 - lng1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lng / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def normalize_business_name(self, name: str) -> str:
        """Normalize business name for comparison"""
        if pd.isna(name):
            return ""
        
        # Convert to lowercase and remove common suffixes/prefixes
        name = str(name).lower().strip()
        
        # Remove common business suffixes
        suffixes = ['llc', 'inc', 'corp', 'ltd', 'co', 'company', 'restaurant', 'cafe', 'shop']
        words = name.split()
        filtered_words = [w for w in words if w not in suffixes]
        
        return ' '.join(filtered_words) if filtered_words else name
    
    def load_csv_files(self, file_patterns: List[str]) -> Dict[str, pd.DataFrame]:
        """Load CSV files matching patterns"""
        dataframes = {}
        
        for pattern in file_patterns:
            # Find files matching pattern
            matching_files = []
            for file in os.listdir('.'):
                if pattern in file and file.endswith('.csv'):
                    matching_files.append(file)
            
            # Load the most recent file for each pattern
            if matching_files:
                latest_file = max(matching_files, key=os.path.getctime)
                try:
                    df = pd.read_csv(latest_file)
                    dataframes[pattern] = df
                    print(f"Loaded {len(df)} records from {latest_file}")
                except Exception as e:
                    print(f"Error loading {latest_file}: {e}")
        
        return dataframes
    
    def standardize_columns(self, df: pd.DataFrame, source_name: str) -> pd.DataFrame:
        """Standardize column names across different sources"""
        
        # Standard column mapping
        column_mapping = {
            # Business name variations
            'name': 'business_name',
            'company_name': 'business_name',
            'business': 'business_name',
            
            # Phone variations
            'phone': 'phone_number',
            'telephone': 'phone_number',
            'contact_phone': 'phone_number',
            
            # Email variations
            'email': 'email_address',
            'contact_email': 'email_address',
            'email_addr': 'email_address',
            
            # Website variations
            'website': 'website_url',
            'url': 'website_url',
            'web': 'website_url',
            
            # Address variations
            'address': 'full_address',
            'street_address': 'full_address',
            'addr': 'full_address',
            
            # Coordinate variations
            'lat': 'latitude',
            'lng': 'longitude',
            'lon': 'longitude',
        }
        
        # Rename columns
        df = df.rename(columns=column_mapping)
        
        # Add source column
        df['data_source'] = source_name
        
        # Ensure required columns exist
        required_columns = [
            'business_name', 'phone_number', 'email_address', 'website_url',
            'full_address', 'city', 'state', 'zip_code', 'latitude', 'longitude',
            'business_category', 'data_source'
        ]
        
        for col in required_columns:
            if col not in df.columns:
                df[col] = ''
        
        return df
    
    def deduplicate_businesses(self, df: pd.DataFrame) -> pd.DataFrame:
        """Remove duplicate businesses based on name and location"""
        
        print(f"Deduplicating {len(df)} businesses...")
        
        # Add normalized name for comparison
        df['normalized_name'] = df['business_name'].apply(self.normalize_business_name)
        
        # Calculate distance from Pittsburgh for all businesses
        df['distance_from_pittsburgh'] = df.apply(
            lambda row: self.calculate_distance(
                self.pittsburgh_lat, self.pittsburgh_lng,
                row['latitude'], row['longitude']
            ), axis=1
        )
        
        # Group by normalized name and find potential duplicates
        duplicates_removed = []
        processed_names = set()
        
        for _, business in df.iterrows():
            normalized = business['normalized_name']
            
            if not normalized or normalized in processed_names:
                continue
            
            # Find all businesses with similar names
            similar_businesses = df[df['normalized_name'] == normalized]
            
            if len(similar_businesses) == 1:
                duplicates_removed.append(business)
            else:
                # For multiple similar businesses, keep the one with most complete data
                best_business = self.select_best_business(similar_businesses)
                duplicates_removed.append(best_business)
            
            processed_names.add(normalized)
        
        result_df = pd.DataFrame(duplicates_removed)
        print(f"After deduplication: {len(result_df)} unique businesses")
        
        return result_df
    
    def select_best_business(self, similar_businesses: pd.DataFrame) -> pd.Series:
        """Select the best business record from similar ones"""
        
        # Score each business based on data completeness
        def score_business(business):
            score = 0
            
            # Points for having data
            if pd.notna(business['email_address']) and business['email_address']:
                score += 10
            if pd.notna(business['phone_number']) and business['phone_number']:
                score += 8
            if pd.notna(business['website_url']) and business['website_url']:
                score += 6
            if pd.notna(business['full_address']) and business['full_address']:
                score += 4
            if pd.notna(business['latitude']) and pd.notna(business['longitude']):
                score += 5
            
            # Prefer certain sources
            source_scores = {
                'google_places': 10,
                'openstreetmap': 8,
                'yellow_pages': 6,
                'government': 7,
                'enhanced': 9
            }
            
            source = str(business['data_source']).lower()
            for key, value in source_scores.items():
                if key in source:
                    score += value
                    break
            
            return score
        
        # Calculate scores and return best
        similar_businesses['score'] = similar_businesses.apply(score_business, axis=1)
        best_business = similar_businesses.loc[similar_businesses['score'].idxmax()]
        
        return best_business
    
    def combine_all_data(self, output_filename: str = None) -> str:
        """Combine data from all sources into master CSV"""
        
        # File patterns to look for
        file_patterns = [
            'openstreetmap_businesses',
            'enhanced_with_emails',
            'government_business_data',
            'pittsburgh_independent_coffee_businesses'  # Your existing data
        ]
        
        # Load all available data files
        dataframes = self.load_csv_files(file_patterns)
        
        if not dataframes:
            print("No data files found to combine!")
            return None
        
        # Standardize and combine all dataframes
        combined_data = []
        
        for source_name, df in dataframes.items():
            print(f"Processing {source_name}...")
            
            # Standardize columns
            standardized_df = self.standardize_columns(df, source_name)
            
            # Filter businesses within 300 miles of Pittsburgh
            if 'latitude' in standardized_df.columns and 'longitude' in standardized_df.columns:
                standardized_df['distance_from_pittsburgh'] = standardized_df.apply(
                    lambda row: self.calculate_distance(
                        self.pittsburgh_lat, self.pittsburgh_lng,
                        row['latitude'], row['longitude']
                    ), axis=1
                )
                
                # Keep only businesses within 300 miles
                standardized_df = standardized_df[standardized_df['distance_from_pittsburgh'] <= 300]
                print(f"  {len(standardized_df)} businesses within 300 miles")
            
            combined_data.append(standardized_df)
        
        # Combine all dataframes
        if combined_data:
            master_df = pd.concat(combined_data, ignore_index=True, sort=False)
            print(f"Combined total: {len(master_df)} businesses")
            
            # Deduplicate
            final_df = self.deduplicate_businesses(master_df)
            
            # Sort by distance from Pittsburgh
            final_df = final_df.sort_values('distance_from_pittsburgh')
            
            # Generate output filename if not provided
            if not output_filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_filename = f"master_business_directory_{timestamp}.csv"
            
            # Save final combined data
            final_columns = [
                'business_name', 'phone_number', 'email_address', 'website_url',
                'full_address', 'city', 'state', 'zip_code', 'latitude', 'longitude',
                'business_category', 'distance_from_pittsburgh', 'data_source',
                'collected_at'
            ]
            
            # Only include columns that exist
            available_columns = [col for col in final_columns if col in final_df.columns]
            final_df = final_df[available_columns]
            
            final_df.to_csv(output_filename, index=False)
            
            # Print summary statistics
            self.print_summary_stats(final_df, output_filename)
            
            return output_filename
        
        return None
    
    def print_summary_stats(self, df: pd.DataFrame, filename: str):
        """Print summary statistics of the combined data"""
        
        print(f"\n📊 MASTER BUSINESS DIRECTORY CREATED: {filename}")
        print("=" * 60)
        
        print(f"📈 TOTAL BUSINESSES: {len(df)}")
        print(f"📧 WITH EMAIL ADDRESSES: {len(df[df['email_address'].notna() & (df['email_address'] != '')])}")
        print(f"📞 WITH PHONE NUMBERS: {len(df[df['phone_number'].notna() & (df['phone_number'] != '')])}")
        print(f"🌐 WITH WEBSITES: {len(df[df['website_url'].notna() & (df['website_url'] != '')])}")
        
        # Email success rate
        email_rate = len(df[df['email_address'].notna() & (df['email_address'] != '')]) / len(df) * 100
        print(f"📊 EMAIL SUCCESS RATE: {email_rate:.1f}%")
        
        # Data sources breakdown
        print(f"\n📋 DATA SOURCES:")
        source_counts = df['data_source'].value_counts()
        for source, count in source_counts.items():
            print(f"  • {source}: {count} businesses")
        
        # Geographic distribution
        if 'distance_from_pittsburgh' in df.columns:
            within_50 = len(df[df['distance_from_pittsburgh'] <= 50])
            within_100 = len(df[df['distance_from_pittsburgh'] <= 100])
            within_200 = len(df[df['distance_from_pittsburgh'] <= 200])
            
            print(f"\n🗺️  GEOGRAPHIC DISTRIBUTION:")
            print(f"  • Within 50 miles: {within_50}")
            print(f"  • Within 100 miles: {within_100}")
            print(f"  • Within 200 miles: {within_200}")
            print(f"  • Within 300 miles: {len(df)}")
        
        print("=" * 60)


def main():
    """Combine all collected data"""
    combiner = MasterDataCombiner()
    
    output_file = combiner.combine_all_data()
    
    if output_file:
        print(f"\n✅ SUCCESS! Master business directory created: {output_file}")
        print("\nThis file contains all your business data from multiple free sources,")
        print("deduplicated and ready for use in your marketing campaigns!")
    else:
        print("\n❌ No data files found to combine.")
        print("Please run the data collection scripts first:")
        print("  1. python openstreetmap_collector.py")
        print("  2. python free_email_finder.py")
        print("  3. python government_data_collector.py")


if __name__ == "__main__":
    main()
