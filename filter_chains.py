#!/usr/bin/env python3
"""
Filter out chain coffee shops to focus on independent businesses
"""
import pandas as pd
import os
from datetime import datetime


def filter_chain_stores():
    """Remove chain coffee shops from the dataset"""
    
    # Find the original coffee businesses CSV file
    output_dir = "output"
    csv_files = [f for f in os.listdir(output_dir) if f.startswith('pittsburgh_coffee_businesses_') and f.endswith('.csv')]
    
    if not csv_files:
        print("❌ No coffee business CSV files found!")
        return
    
    # Use the original file (not the ones with emails)
    original_file = None
    for file in csv_files:
        if 'with_emails' not in file:
            original_file = file
            break
    
    if not original_file:
        print("❌ Original coffee business file not found!")
        return
    
    csv_file_path = os.path.join(output_dir, original_file)
    print(f"📁 Processing file: {original_file}")
    
    # Read the CSV file
    df = pd.read_csv(csv_file_path)
    print(f"📊 Total businesses before filtering: {len(df)}")
    
    # Define chain store patterns to remove
    chain_patterns = [
        'starbucks',
        'dunkin',
        'tim hortons',
        'tim horton',
        'mcdonald',
        'burger king',
        'subway',
        'panera',
        'panera bread',
        'einstein bros',
        'caribou coffee',
        'peet\'s coffee',
        'peets coffee',
        'costa coffee',
        'second cup',
        'gloria jean',
        'tully\'s coffee',
        'the coffee bean & tea leaf',
        'biggby coffee',
        'dutch bros'
    ]
    
    # Filter out chain stores
    original_count = len(df)
    
    # Create a mask for businesses to keep (not chains)
    keep_mask = pd.Series([True] * len(df))
    
    for pattern in chain_patterns:
        # Check business name (case insensitive)
        chain_mask = df['business_name'].str.contains(pattern, case=False, na=False)
        keep_mask = keep_mask & ~chain_mask
        
        removed_count = chain_mask.sum()
        if removed_count > 0:
            print(f"  🚫 Removing {removed_count} '{pattern}' locations")
    
    # Apply the filter
    df_filtered = df[keep_mask].copy()
    
    print(f"\n📈 FILTERING RESULTS:")
    print(f"Original businesses: {original_count}")
    print(f"Chain stores removed: {original_count - len(df_filtered)}")
    print(f"Independent businesses remaining: {len(df_filtered)}")
    print(f"Percentage kept: {len(df_filtered)/original_count*100:.1f}%")
    
    # Save filtered dataset
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filtered_filename = f"pittsburgh_independent_coffee_businesses_{timestamp}.csv"
    filtered_path = os.path.join(output_dir, filtered_filename)
    
    df_filtered.to_csv(filtered_path, index=False)
    
    print(f"\n✅ Filtered dataset saved as: {filtered_filename}")
    
    # Show some statistics about the remaining businesses
    print(f"\n📊 INDEPENDENT BUSINESS BREAKDOWN:")
    
    # By region
    if 'collection_region' in df_filtered.columns:
        region_counts = df_filtered['collection_region'].value_counts()
        print(f"\n📍 By Region:")
        for region, count in region_counts.items():
            print(f"  {region}: {count}")
    
    # Businesses with websites
    with_websites = df_filtered['website_url'].notna().sum()
    print(f"\n🌐 Businesses with websites: {with_websites} ({with_websites/len(df_filtered)*100:.1f}%)")
    
    # Show some examples of remaining businesses
    print(f"\n☕ SAMPLE INDEPENDENT COFFEE BUSINESSES:")
    sample_businesses = df_filtered['business_name'].head(10).tolist()
    for i, business in enumerate(sample_businesses, 1):
        print(f"  {i}. {business}")
    
    return filtered_path


def main():
    """Main function"""
    print("=" * 70)
    print("🔧 FILTERING CHAIN COFFEE STORES")
    print("=" * 70)
    print("Removing: Starbucks, Dunkin', Tim Hortons, and other chains")
    print("Focus: Independent coffee shops and local businesses")
    print()
    
    filtered_file = filter_chain_stores()
    
    if filtered_file:
        print(f"\n🎉 SUCCESS!")
        print(f"📁 Filtered file: {filtered_file}")
        print(f"\n🚀 Ready to run email extraction on independent businesses only!")
        print(f"Next step: Update email_scraper.py to use the new filtered file")


if __name__ == '__main__':
    main()
