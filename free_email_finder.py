"""
Free Email Discovery Tools
Enhance your existing business data with email addresses using free methods
"""
import requests
import re
import time
import whois
from urllib.parse import urlparse
from typing import List, Optional, Dict
import csv
from datetime import datetime


class FreeEmailFinder:
    """Find business email addresses using free methods"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Common email patterns
        self.email_patterns = [
            'info@{}',
            'contact@{}',
            'hello@{}',
            'admin@{}',
            'support@{}',
            'sales@{}',
            'office@{}',
            'mail@{}',
            'business@{}',
            'inquiries@{}'
        ]
        
        # Email regex patterns
        self.email_regex = re.compile(
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        )
        
    def extract_domain_from_url(self, url: str) -> Optional[str]:
        """Extract domain from URL"""
        try:
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            # Remove www. prefix
            if domain.startswith('www.'):
                domain = domain[4:]
            return domain
        except:
            return None
    
    def generate_email_patterns(self, domain: str) -> List[str]:
        """Generate common email patterns for domain"""
        return [pattern.format(domain) for pattern in self.email_patterns]
    
    def search_google_for_emails(self, domain: str, business_name: str) -> List[str]:
        """Search Google for email addresses (free method)"""
        emails = set()
        
        # Search queries
        queries = [
            f'site:{domain} email',
            f'site:{domain} contact',
            f'"{business_name}" email',
            f'"{business_name}" contact'
        ]
        
        for query in queries:
            try:
                # Use DuckDuckGo instead of Google (no API key needed)
                search_url = f"https://html.duckduckgo.com/html/?q={query}"
                response = self.session.get(search_url, timeout=10)
                
                if response.status_code == 200:
                    # Extract emails from search results
                    found_emails = self.email_regex.findall(response.text)
                    for email in found_emails:
                        if domain in email.lower():
                            emails.add(email.lower())
                
                time.sleep(2)  # Be respectful
                
            except Exception as e:
                print(f"Error searching for {query}: {e}")
                continue
        
        return list(emails)
    
    def get_whois_contact_info(self, domain: str) -> List[str]:
        """Get contact information from WHOIS database"""
        emails = set()
        
        try:
            w = whois.whois(domain)
            
            # Extract emails from various WHOIS fields
            fields_to_check = ['emails', 'registrant_email', 'admin_email', 'tech_email']
            
            for field in fields_to_check:
                if hasattr(w, field):
                    value = getattr(w, field)
                    if value:
                        if isinstance(value, list):
                            for email in value:
                                if email and '@' in str(email):
                                    emails.add(str(email).lower())
                        elif '@' in str(value):
                            emails.add(str(value).lower())
            
        except Exception as e:
            print(f"Error getting WHOIS for {domain}: {e}")
        
        return list(emails)
    
    def scrape_website_for_emails(self, url: str) -> List[str]:
        """Scrape website for email addresses"""
        emails = set()
        
        try:
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            
            # Find all email addresses in the page
            found_emails = self.email_regex.findall(response.text)
            
            # Filter out common non-business emails
            excluded_domains = [
                'example.com', 'test.com', 'gmail.com', 'yahoo.com', 
                'hotmail.com', 'outlook.com', 'facebook.com', 'twitter.com'
            ]
            
            for email in found_emails:
                email = email.lower()
                if not any(excluded in email for excluded in excluded_domains):
                    emails.add(email)
            
        except Exception as e:
            print(f"Error scraping {url}: {e}")
        
        return list(emails)
    
    def find_emails_for_business(self, business_name: str, website_url: str) -> Dict[str, List[str]]:
        """Find all possible emails for a business using free methods"""
        results = {
            'pattern_emails': [],
            'scraped_emails': [],
            'whois_emails': [],
            'search_emails': []
        }
        
        if not website_url:
            return results
        
        domain = self.extract_domain_from_url(website_url)
        if not domain:
            return results
        
        print(f"Finding emails for {business_name} ({domain})")
        
        # Method 1: Generate common email patterns
        results['pattern_emails'] = self.generate_email_patterns(domain)
        
        # Method 2: Scrape website
        results['scraped_emails'] = self.scrape_website_for_emails(website_url)
        
        # Method 3: WHOIS lookup
        results['whois_emails'] = self.get_whois_contact_info(domain)
        
        # Method 4: Search engines
        results['search_emails'] = self.search_google_for_emails(domain, business_name)
        
        return results
    
    def get_best_email_candidates(self, email_results: Dict[str, List[str]]) -> List[str]:
        """Get the most likely valid email addresses"""
        candidates = []
        
        # Priority order: scraped > whois > search > patterns
        for scraped in email_results['scraped_emails']:
            candidates.append(('scraped', scraped))
        
        for whois_email in email_results['whois_emails']:
            candidates.append(('whois', whois_email))
        
        for search_email in email_results['search_emails']:
            candidates.append(('search', search_email))
        
        # Add top pattern emails
        for pattern in email_results['pattern_emails'][:3]:
            candidates.append(('pattern', pattern))
        
        return candidates
    
    def enhance_business_data_with_emails(self, csv_file: str, output_file: str):
        """Enhance existing business CSV with email addresses"""
        
        enhanced_businesses = []
        
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            businesses = list(reader)
        
        print(f"Processing {len(businesses)} businesses for email enhancement...")
        
        for i, business in enumerate(businesses):
            print(f"Processing {i+1}/{len(businesses)}: {business['business_name']}")
            
            # Skip if already has email
            if business.get('email_address'):
                enhanced_businesses.append(business)
                continue
            
            # Find emails
            email_results = self.find_emails_for_business(
                business['business_name'], 
                business.get('website_url', '')
            )
            
            # Get best candidates
            candidates = self.get_best_email_candidates(email_results)
            
            # Add email information
            business['email_candidates'] = '; '.join([f"{method}:{email}" for method, email in candidates[:5]])
            business['email_found_count'] = len(candidates)
            
            # Set primary email (best candidate)
            if candidates:
                business['email_address'] = candidates[0][1]
                business['email_source'] = candidates[0][0]
            else:
                business['email_source'] = 'none_found'
            
            enhanced_businesses.append(business)
            
            # Be respectful with delays
            time.sleep(3)
        
        # Save enhanced data
        if enhanced_businesses:
            fieldnames = list(enhanced_businesses[0].keys())
            
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(enhanced_businesses)
            
            print(f"Enhanced data saved to {output_file}")
            
            # Print statistics
            with_emails = sum(1 for b in enhanced_businesses if b.get('email_address'))
            print(f"Email enhancement results:")
            print(f"- Total businesses: {len(enhanced_businesses)}")
            print(f"- Businesses with emails: {with_emails}")
            print(f"- Email success rate: {with_emails/len(enhanced_businesses)*100:.1f}%")


def main():
    """Example usage"""
    finder = FreeEmailFinder()
    
    # Example: enhance existing CSV file with emails
    input_file = "openstreetmap_businesses_20250730_120000.csv"  # Replace with your file
    output_file = f"enhanced_with_emails_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    try:
        finder.enhance_business_data_with_emails(input_file, output_file)
    except FileNotFoundError:
        print(f"Input file {input_file} not found. Please run openstreetmap_collector.py first.")
        
        # Demo with single business
        print("\nDemo: Finding emails for a single business")
        results = finder.find_emails_for_business("Starbucks", "starbucks.com")
        candidates = finder.get_best_email_candidates(results)
        
        print("Email candidates found:")
        for method, email in candidates[:5]:
            print(f"  {method}: {email}")


if __name__ == "__main__":
    main()
